# -*- mode: python ; coding: utf-8 -*-
# ملف تكوين PyInstaller لنظام إدارة المستندات المحاسبية

import os

# المسار الحالي
current_dir = os.path.dirname(os.path.abspath(SPEC))

a = Analysis(
    ['start.py'],  # الملف الرئيسي المحسن
    pathex=[current_dir],
    binaries=[],
    datas=[
        ('excel_manager.py', '.'),
        ('document_window.py', '.'),
        ('search_window.py', '.'),
        ('manage_accounts.py', '.'),
        ('app.py', '.'),
    ],
    hiddenimports=[
        'openpyxl',
        'openpyxl.styles',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'ttkthemes',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
    ],
    noarchive=False,
    optimize=2,
)

pyz = PYZ(a.pure, a.zipped_data)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='نظام_المحاسبة_وزارة_الصحة',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
