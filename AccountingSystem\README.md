# نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية

## 📋 وصف النظام

نظام محاسبي متكامل لإدارة المستندات المالية والحوالات المحاسبية، مصمم خصيصاً لوزارة الصحة الأردنية.

## ✨ المميزات

- **إدارة الحسابات**: إنشاء وتعديل وحذف الحسابات المحاسبية
- **إدارة المستندات**: إضافة مستندات الصرف مع أرقام التأدية
- **التقارير**: إنشاء تقارير شاملة للحسابات والمستندات
- **البحث**: البحث السريع في المستندات والحسابات
- **واجهة عربية**: دعم كامل للغة العربية والكتابة من اليمين لليسار
- **تصدير Excel**: حفظ البيانات في ملفات Excel منسقة

## 🛠️ متطلبات التشغيل

### المكتبات المطلوبة:
- Python 3.7 أو أحدث
- openpyxl >= 3.0.0
- ttkthemes >= 3.2.2 (اختياري)
- tkinter (مدمج مع Python)

### تثبيت المكتبات:
```bash
pip install -r requirements.txt
```

## 🚀 طرق التشغيل

### 1. التشغيل المباشر (الأسهل):
```bash
python main.py
```

### 2. التشغيل المحسن:
```bash
python start.py
```

### 3. التشغيل عبر ملف Batch (Windows):
```bash
run_system.bat
```

### 4. إنشاء ملف تنفيذي:
```bash
python build.py
```

## 📁 هيكل الملفات

```
AccountingSystem/
├── main.py                 # الملف الرئيسي المبسط
├── start.py               # الملف الرئيسي المحسن
├── app.py                 # التطبيق الرئيسي
├── excel_manager.py       # إدارة ملفات Excel
├── document_window.py     # نافذة إضافة المستندات
├── search_window.py       # نافذة البحث
├── manage_accounts.py     # إدارة الحسابات
├── build.py              # بناء الملف التنفيذي
├── requirements.txt      # المكتبات المطلوبة
├── run_system.bat       # ملف تشغيل Windows
└── accounting_system.xlsx # ملف البيانات
```

## 📊 استخدام النظام

### إنشاء حساب جديد:
1. اختر "الحسابات" من القائمة
2. اختر "إضافة حساب"
3. أدخل رقم الحساب واسمه والرصيد الافتتاحي

### إضافة مستند:
1. اختر "المستندات" من القائمة
2. اختر "إضافة مستند"
3. اختر الحساب وأدخل بيانات المستند

### إنشاء التقارير:
1. اختر "التقارير" من القائمة
2. اختر نوع التقرير المطلوب

## 🔧 استكشاف الأخطاء

### مشكلة: "python غير معروف"
**الحل**: تأكد من تثبيت Python وإضافته لمتغير PATH

### مشكلة: "openpyxl غير موجود"
**الحل**: 
```bash
pip install openpyxl
```

### مشكلة: خطأ في الترميز العربي
**الحل**: تأكد من أن النظام يدعم UTF-8

## 📞 الدعم الفني

للمساعدة والدعم الفني، يرجى التواصل مع فريق تطوير النظام.

## 📄 الترخيص

هذا النظام مطور خصيصاً لوزارة الصحة الأردنية.

---
**تم التطوير بواسطة**: فريق تطوير الأنظمة المحاسبية
**التاريخ**: 2024
