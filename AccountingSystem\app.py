import openpyxl
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
# from ttkthemes import ThemedTk  # نستخدم Tk العادي بدلاً منه
from excel_manager import ExcelManager
from document_window import AddDocumentWindow
from search_window import SearchWindow
from manage_accounts import ManageAccountsDialog
from datetime import datetime

class AccountingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        self.root.geometry("1200x700")
        self.excel = ExcelManager()

        # تعريف الخط العربي
        self.root.option_add("*font", "Arial 12")
        self.root.configure(bg='#f0f0f0')

        # إعداد إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.create_menu()
        self.create_main_frame()
        self.create_status_bar()

    def on_closing(self):
        """معالجة إغلاق النافذة مع حفظ تلقائي"""
        try:
            # حفظ تلقائي قبل الإغلاق
            self.excel.save_workbook()
            self.root.destroy()
        except Exception as e:
            if messagebox.askyesno("خطأ في الحفظ",
                                 f"حدث خطأ في الحفظ: {str(e)}\nهل تريد الإغلاق بدون حفظ؟"):
                self.root.destroy()

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Label(self.root, text="جاهز",
                                  bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=message)
        self.root.update_idletasks()
        
    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="فتح ملف", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_file)
        file_menu.add_command(label="حفظ باسم", command=self.save_as)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة الحسابات
        accounts_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الحسابات", menu=accounts_menu)
        accounts_menu.add_command(label="إضافة حساب", command=self.show_add_account)
        accounts_menu.add_command(label="إدارة الحسابات", command=self.show_manage_accounts)
        
        # قائمة المستندات
        docs_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المستندات", menu=docs_menu)
        docs_menu.add_command(label="إضافة مستند", command=self.show_add_document)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="التقرير الإجمالي", command=self.show_summary_report)
        reports_menu.add_command(label="تقرير الحسابات", command=self.show_report)
        reports_menu.add_command(label="تقرير المستندات", command=self.show_documents_report)
    
    def create_main_frame(self):
        """إنشاء الإطار الرئيسي للتطبيق"""
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # الأزرار الرئيسية
        ttk.Button(self.main_frame, text="إضافة حساب", 
                  command=self.show_add_account).grid(row=0, column=0, padx=5, pady=5)
        
        ttk.Button(self.main_frame, text="إضافة مستند",
                  command=self.show_add_document).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(self.main_frame, text="البحث",
                  command=self.show_search).grid(row=0, column=2, padx=5, pady=5)

        ttk.Button(self.main_frame, text="التقرير الإجمالي",
                  command=self.show_summary_report).grid(row=0, column=3, padx=5, pady=5)

        ttk.Button(self.main_frame, text="التقارير",
                  command=self.show_report).grid(row=0, column=4, padx=5, pady=5)
    
    def show_add_account(self):
        AddAccountDialog(self.root, self.excel)
    
    def show_manage_accounts(self):
        # استخدام النافذة المحسنة من manage_accounts.py
        try:
            from manage_accounts import ManageAccountsDialog
            ManageAccountsDialog(self.root, self.excel)
        except ImportError:
            # في حالة عدم وجود الملف، استخدم النافذة القديمة
            ManageAccountsDialogOld(self.root, self.excel)
    
    def show_add_document(self):
        AddDocumentWindow(self)
    
    def show_search(self):
        SearchWindow(self)
    
    def show_summary_report(self):
        """عرض التقرير الإجمالي مع خيارات الطباعة"""
        try:
            self.update_status("جاري إنشاء التقرير الإجمالي...")
            result = self.excel.create_summary_report()

            if result:
                self.update_status("تم إنشاء التقرير الإجمالي بنجاح")

                # إنشاء نافذة خيارات التقرير
                dialog = tk.Toplevel(self.root)
                dialog.title("التقرير الإجمالي")
                dialog.geometry("400x200")
                dialog.resizable(False, False)

                # توسيط النافذة
                dialog.transient(self.root)
                dialog.grab_set()

                # العنوان
                title_label = ttk.Label(dialog, text="تم إنشاء التقرير الإجمالي بنجاح",
                                      font=("Arial", 12, "bold"))
                title_label.pack(pady=20)

                # إطار الأزرار
                buttons_frame = ttk.Frame(dialog)
                buttons_frame.pack(pady=20)

                # زر طباعة التقرير
                print_btn = ttk.Button(buttons_frame, text="طباعة التقرير",
                                     command=lambda: self.print_summary_report(dialog))
                print_btn.pack(side=tk.LEFT, padx=10)

                # زر تصدير التقرير
                export_btn = ttk.Button(buttons_frame, text="تصدير التقرير",
                                      command=lambda: self.export_summary_report(dialog))
                export_btn.pack(side=tk.LEFT, padx=10)

                # زر إغلاق
                close_btn = ttk.Button(buttons_frame, text="إغلاق",
                                     command=dialog.destroy)
                close_btn.pack(side=tk.LEFT, padx=10)

            else:
                self.update_status("فشل في إنشاء التقرير الإجمالي")
                messagebox.showerror("خطأ", "فشل في إنشاء التقرير الإجمالي")

        except Exception as e:
            self.update_status("خطأ في إنشاء التقرير الإجمالي")
            messagebox.showerror("خطأ", str(e))

    def print_summary_report(self, parent_dialog=None):
        """طباعة التقرير الإجمالي"""
        try:
            import tempfile
            import webbrowser
            from datetime import datetime

            # التحقق من وجود التقرير
            if 'التقرير الإجمالي' not in self.excel.workbook.sheetnames:
                messagebox.showerror("خطأ", "التقرير الإجمالي غير موجود")
                return

            ws = self.excel.workbook['التقرير الإجمالي']

            # إنشاء محتوى HTML للطباعة
            html_content = self.generate_report_html(ws)

            # حفظ الملف المؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة
            webbrowser.open(f'file://{temp_file}')

            messagebox.showinfo("طباعة", "تم فتح التقرير في المتصفح للطباعة")

            if parent_dialog:
                parent_dialog.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إعداد الطباعة: {str(e)}")

    def export_summary_report(self, parent_dialog=None):
        """تصدير التقرير الإجمالي إلى ملف Excel"""
        try:
            from tkinter import filedialog
            from datetime import datetime
            import openpyxl

            # التحقق من وجود التقرير
            if 'التقرير الإجمالي' not in self.excel.workbook.sheetnames:
                messagebox.showerror("خطأ", "التقرير الإجمالي غير موجود")
                return

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="تصدير التقرير الإجمالي",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"التقرير_الإجمالي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not filename:
                return

            # إنشاء ملف Excel جديد مع التقرير فقط
            new_wb = openpyxl.Workbook()
            new_ws = new_wb.active
            new_ws.title = "التقرير الإجمالي"

            # نسخ محتوى التقرير
            source_ws = self.excel.workbook['التقرير الإجمالي']

            # نسخ البيانات
            for row in source_ws.iter_rows():
                for cell in row:
                    new_cell = new_ws.cell(row=cell.row, column=cell.column)
                    new_cell.value = cell.value
                    if cell.font:
                        new_cell.font = cell.font
                    if cell.fill:
                        new_cell.fill = cell.fill
                    if cell.border:
                        new_cell.border = cell.border
                    if cell.alignment:
                        new_cell.alignment = cell.alignment
                    if cell.number_format:
                        new_cell.number_format = cell.number_format

            # نسخ عرض الأعمدة
            for col in source_ws.column_dimensions:
                new_ws.column_dimensions[col].width = source_ws.column_dimensions[col].width

            # تعيين اتجاه الصفحة
            new_ws.sheet_properties.rightToLeft = True
            new_ws.sheet_view.rightToLeft = True

            # حفظ الملف
            new_wb.save(filename)

            messagebox.showinfo("نجاح", f"تم تصدير التقرير إلى:\n{filename}")

            if parent_dialog:
                parent_dialog.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def generate_report_html(self, ws):
        """إنشاء محتوى HTML للتقرير"""
        from datetime import datetime

        # بناء جدول البيانات
        table_rows = ""

        # العناوين
        headers = ['رقم الحساب', 'اسم الحساب', 'الرصيد الافتتاحي', 'مجموع المستندات', 'الرصيد النهائي', 'عدد المستندات']
        header_row = "".join([f"<th>{header}</th>" for header in headers])

        # البيانات
        for row in range(7, ws.max_row + 1):
            if ws.cell(row=row, column=1).value:  # إذا كان هناك بيانات
                row_data = ""
                for col in range(1, 7):
                    cell_value = ws.cell(row=row, column=col).value or ""
                    row_data += f"<td>{cell_value}</td>"
                table_rows += f"<tr>{row_data}</tr>"

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>التقرير الإجمالي لأرصدة الحسابات</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .report-table {{ width: 100%; border-collapse: collapse; }}
                .report-table th, .report-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .report-table th {{ background-color: #366092; color: white; font-weight: bold; }}
                .report-table tr:nth-child(even) {{ background-color: #f2f2f2; }}
                @media print {{
                    body {{ margin: 0; }}
                    .no-print {{ display: none; }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>المملكة الأردنية الهاشمية</h1>
                <h2>وزارة الصحة</h2>
                <h3>التقرير الإجمالي لأرصدة الحسابات</h3>
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y/%m/%d %H:%M')}</p>
            </div>

            <table class="report-table">
                <thead>
                    <tr>{header_row}</tr>
                </thead>
                <tbody>
                    {table_rows}
                </tbody>
            </table>

            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
        """

        return html_content

    def show_report(self):
        try:
            self.update_status("جاري إنشاء التقرير...")
            self.excel.create_report()
            self.update_status("تم إنشاء التقرير بنجاح")
            messagebox.showinfo("نجاح", "تم إنشاء التقرير بنجاح")
        except Exception as e:
            self.update_status("خطأ في إنشاء التقرير")
            messagebox.showerror("خطأ", str(e))
    
    def show_documents_report(self):
        """عرض تقرير المستندات"""
        try:
            # إنشاء نافذة منبثقة لاختيار الحساب
            dialog = tk.Toplevel(self.root)
            dialog.title("اختيار الحساب")
            dialog.geometry("300x150")
            
            ttk.Label(dialog, text="اختر الحساب:").pack(pady=10)
            
            # قائمة منسدلة للحسابات
            accounts = [sheet for sheet in self.excel.workbook.sheetnames 
                       if sheet not in ['التقارير', 'تقرير المستندات']]
            accounts.insert(0, "جميع الحسابات")
            
            account_var = tk.StringVar()
            account_combo = ttk.Combobox(dialog, textvariable=account_var, values=accounts)
            account_combo.current(0)
            account_combo.pack(pady=10)
            
            def generate_report():
                selected = account_var.get()
                account = None if selected == "جميع الحسابات" else selected
                if self.excel.create_documents_report(account):
                    messagebox.showinfo("نجاح", "تم إنشاء تقرير المستندات بنجاح")
                dialog.destroy()
            
            ttk.Button(dialog, text="إنشاء التقرير", 
                      command=generate_report).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("خطأ", str(e))
    
    def open_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            try:
                self.excel = ExcelManager()
                self.excel.workbook = openpyxl.load_workbook(file_path)
                self.excel.current_file = file_path
                self.excel.file_path = file_path
                self.update_status(f"تم فتح الملف: {file_path}")
                messagebox.showinfo("نجاح", "تم فتح الملف بنجاح")
            except Exception as e:
                self.update_status("خطأ في فتح الملف")
                messagebox.showerror("خطأ", str(e))
    
    def save_file(self):
        try:
            self.update_status("جاري حفظ الملف...")
            if self.excel.save_workbook():
                self.update_status("تم حفظ الملف بنجاح")
                messagebox.showinfo("نجاح", "تم حفظ الملف بنجاح")
            else:
                self.update_status("فشل في حفظ الملف")
        except Exception as e:
            self.update_status("خطأ في حفظ الملف")
            messagebox.showerror("خطأ", str(e))
    
    def save_as(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            try:
                self.update_status("جاري حفظ الملف...")
                if self.excel.save_workbook(file_path):
                    self.update_status(f"تم حفظ الملف: {file_path}")
                    messagebox.showinfo("نجاح", "تم حفظ الملف بنجاح")
                else:
                    self.update_status("فشل في حفظ الملف")
            except Exception as e:
                self.update_status("خطأ في حفظ الملف")
                messagebox.showerror("خطأ", str(e))

# تم نقل ManageAccountsDialog إلى ملف منفصل manage_accounts.py
# هذا الكلاس محفوظ للتوافق مع النسخ القديمة
class ManageAccountsDialogOld(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("إدارة الحسابات - النسخة القديمة")
        self.excel = excel

        # Create listbox to show accounts
        self.accounts_list = tk.Listbox(self, width=40, height=10)
        self.accounts_list.grid(row=0, column=0, columnspan=2, padx=5, pady=5)

        # Load accounts into listbox
        for sheet in self.excel.workbook.sheetnames:
            if sheet not in ['التقارير', 'تقرير المستندات']:
                self.accounts_list.insert(tk.END, sheet)

        # Add buttons
        ttk.Button(self, text="حذف الحساب", command=self.delete_account).grid(row=1, column=0, pady=5)
        ttk.Button(self, text="إغلاق", command=self.destroy).grid(row=1, column=1, pady=5)

    def delete_account(self):
        selection = self.accounts_list.curselection()
        if selection:
            account = self.accounts_list.get(selection[0])
            if messagebox.askyesno("تأكيد", f"هل أنت متأكد من حذف الحساب {account}؟"):
                try:
                    del self.excel.workbook[account]
                    self.excel.save_workbook()  # حفظ التغييرات
                    self.accounts_list.delete(selection[0])
                    messagebox.showinfo("نجاح", "تم حذف الحساب بنجاح")
                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف الحساب: {str(e)}")

class AddAccountDialog(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("إضافة حساب جديد")
        self.excel = excel
        
        # حقول الإدخال
        ttk.Label(self, text="رقم الحساب:").grid(row=0, column=0, padx=5, pady=5)
        self.account_num = ttk.Entry(self)
        self.account_num.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self, text="اسم الحساب:").grid(row=1, column=0, padx=5, pady=5)
        self.account_name = ttk.Entry(self)
        self.account_name.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self, text="الرصيد الأولي:").grid(row=2, column=0, padx=5, pady=5)
        self.balance = ttk.Entry(self)
        self.balance.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Button(self, text="إضافة", 
                  command=self.add_account).grid(row=3, column=0, columnspan=2, pady=10)
    
    def add_account(self):
        try:
            # التحقق من صحة البيانات
            account_num = self.account_num.get().strip()
            account_name = self.account_name.get().strip()
            balance_text = self.balance.get().strip()

            # التحقق من الحقول المطلوبة
            if not account_num:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الحساب")
                return

            if not account_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                return

            # التحقق من الرصيد
            try:
                initial_balance = float(balance_text) if balance_text else 0
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيمة رقمية صحيحة للرصيد الأولي")
                return

            # محاولة إنشاء الحساب
            result = self.excel.create_account_sheet(account_num, account_name, initial_balance)

            if result:
                messagebox.showinfo("نجاح", f"تم إضافة الحساب '{account_name}' بنجاح")
                self.destroy()
            else:
                # الخطأ سيظهر من ExcelManager
                pass

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

if __name__ == '__main__':
    try:
        # محاولة استخدام ThemedTk أولاً
        try:
            from ttkthemes import ThemedTk
            root = ThemedTk(theme="arc")
        except ImportError:
            # في حالة عدم توفر ttkthemes، استخدم Tk العادي
            root = tk.Tk()

        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        root.geometry("1200x700")
        app = AccountingApp(root)
        root.mainloop()

    except Exception as e:
        print(f"Error: {str(e)}")
        try:
            # في حالة فشل أي شيء، نستخدم النافذة العادية
            root = tk.Tk()
            root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
            root.geometry("1200x700")
            app = AccountingApp(root)
            root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل النظام: {str(e)}")
