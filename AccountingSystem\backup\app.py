import openpyxl
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from excel_manager import ExcelManager
from document_window import AddDocumentWindow
from search_window import SearchWindow
from datetime import datetime

class AccountingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة المستندات - وزارة الصحة")
        self.root.geometry("1000x600")
        self.excel = ExcelManager()
        
        # تعريف الخط العربي
        self.root.option_add("*font", "Arial 12")
        self.root.configure(bg='#f0f0f0')
        
        self.create_menu()
        self.create_main_frame()
        
    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="فتح ملف", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_file)
        file_menu.add_command(label="حفظ باسم", command=self.save_as)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة الحسابات
        accounts_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الحسابات", menu=accounts_menu)
        accounts_menu.add_command(label="إضافة حساب", command=self.show_add_account)
        accounts_menu.add_command(label="إدارة الحسابات", command=self.show_manage_accounts)
        
        # قائمة المستندات
        docs_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المستندات", menu=docs_menu)
        docs_menu.add_command(label="إضافة مستند", command=self.show_add_document)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير الحسابات", command=self.show_report)
        reports_menu.add_command(label="بحث", command=self.show_search)
    
    def create_main_frame(self):
        """إنشاء الإطار الرئيسي للتطبيق"""
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # الأزرار الرئيسية
        ttk.Button(self.main_frame, text="إضافة حساب", 
                  command=self.show_add_account).grid(row=0, column=0, padx=5, pady=5)
        
        ttk.Button(self.main_frame, text="إضافة مستند",
                  command=self.show_add_document).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(self.main_frame, text="البحث",
                  command=self.show_search).grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Button(self.main_frame, text="التقارير",
                  command=self.show_report).grid(row=0, column=3, padx=5, pady=5)
    
    def show_add_account(self):
        AddAccountDialog(self.root, self.excel)
    
    def show_manage_accounts(self):
        ManageAccountsDialog(self.root, self.excel)
    
    def show_add_document(self):
        AddDocumentWindow(self)
    
    def show_search(self):
        SearchWindow(self)
    
    def show_report(self):
        try:
            self.excel.create_report()
            messagebox.showinfo("نجاح", "تم إنشاء التقرير بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", str(e))
    
    def open_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            try:
                self.excel = ExcelManager()
                self.excel.workbook = openpyxl.load_workbook(file_path)
                self.excel.current_file = file_path
                messagebox.showinfo("نجاح", "تم فتح الملف بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", str(e))
    
    def save_file(self):
        if not self.excel.file_path:
            self.save_as()
        else:
            try:
                self.excel.save()
                messagebox.showinfo("نجاح", "تم حفظ الملف بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", str(e))
    
    def save_as(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            try:
                self.excel.save(file_path)
                messagebox.showinfo("نجاح", "تم حفظ الملف بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", str(e))

class AddAccountDialog(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("إضافة حساب جديد")
        self.excel = excel
        
        # حقول الإدخال
        ttk.Label(self, text="رقم الحساب:").grid(row=0, column=0, padx=5, pady=5)
        self.account_num = ttk.Entry(self)
        self.account_num.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self, text="اسم الحساب:").grid(row=1, column=0, padx=5, pady=5)
        self.account_name = ttk.Entry(self)
        self.account_name.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self, text="الرصيد الأولي:").grid(row=2, column=0, padx=5, pady=5)
        self.balance = ttk.Entry(self)
        self.balance.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Button(self, text="إضافة", 
                  command=self.add_account).grid(row=3, column=0, columnspan=2, pady=10)
    
    def add_account(self):
        try:
            self.excel.create_account_sheet(
                self.account_num.get(),
                self.account_name.get(),
                float(self.balance.get() or 0)
            )
            messagebox.showinfo("نجاح", "تم إضافة الحساب بنجاح")
            self.destroy()
        except Exception as e:
            messagebox.showerror("خطأ", str(e))

def main():
    root = tk.Tk()
    app = AccountingApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
