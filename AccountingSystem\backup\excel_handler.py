import openpyxl
from openpyxl.styles import Font, Align<PERSON>, PatternFill, Border, Side
from datetime import datetime
import os

class ExcelHandler:
    def __init__(self, file_path=None):
        self.file_path = file_path or "accounting_system.xlsx"
        if os.path.exists(self.file_path):
            self.wb = openpyxl.load_workbook(self.file_path)
        else:
            self.wb = openpyxl.Workbook()
            if 'Sheet' in self.wb.sheetnames:
                self.wb.remove(self.wb['Sheet'])
            self.save()
    
    def create_account_sheet(self, account_num, account_name, initial_balance=0):
        """إنشاء صفحة حساب جديدة"""
        # التحقق من عدم وجود الحساب مسبقاً
        sheet_name = f"{account_num}-{account_name}"
        if sheet_name in self.wb.sheetnames:
            raise ValueError("هذا الحساب موجود مسبقاً")
        
        # إنشاء الورقة
        ws = self.wb.create_sheet(sheet_name)
        ws.sheet_properties.rightToLeft = True
        
        # ترويسة الصفحة
        self._setup_header(ws)
        
        # إعداد الأقسام الستة
        self._setup_sections(ws, initial_balance)
        
        # حفظ التغييرات
        self.save()
        
    def _setup_header(self, ws):
        """إعداد ترويسة الصفحة"""
        # عنوان الصفحة
        ws.merge_cells('A1:R1')
        ws['A1'] = "المملكة الأردنية الهاشمية"
        ws['A1'].font = Font(size=14, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center')
        
        # معلومات الوزارة
        ws.merge_cells('A2:R2')
        ws['A2'] = "وزارة / الدائرة : وزارة الصحة"
        ws['A2'].alignment = Alignment(horizontal='center')
        
        # معلومات الشهر والسنة
        ws.merge_cells('A4:L4')
        ws['A4'] = f"الشهر: {datetime.now().strftime('%B')} سنة: {datetime.now().year}"
        
        ws.merge_cells('M4:R4')
        ws['M4'] = "المركز: التأمين الصحي"
        
    def _setup_sections(self, ws, initial_balance):
        """إعداد الأقسام الستة للمستندات"""
        for i in range(6):
            col_start = chr(65 + (i * 3))  # A, D, G, J, M, P
            
            # العناوين
            headers = [
                ("فلس/دينار", col_start),
                ("مستند الصرف", chr(ord(col_start) + 1)),
                ("رقم التأدية", chr(ord(col_start) + 2))
            ]
            
            for header, col in headers:
                cell = ws[f'{col}8']
                cell.value = header
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.border = Border(all=Side(style='thin'))
            
            # القيم الأولية
            ws[f'{col_start}9'] = initial_balance if i == 0 else f"=SUM({chr(ord(col_start)-3)}34)"
            ws[f'{chr(ord(col_start) + 1)}9'] = "ما قبله"
            
            # صيغ المجموع
            ws[f'{col_start}33'] = f"=SUM({col_start}9:{col_start}32)"
    
    def add_document(self, sheet_name, amount, doc_num, pay_num):
        """إضافة مستند جديد"""
        ws = self.wb[sheet_name]
        cell = self._find_empty_cell(ws)
        
        if not cell:
            return False
            
        row, col = cell
        ws.cell(row=row, column=col).value = amount
        ws.cell(row=row, column=col+1).value = doc_num
        ws.cell(row=row, column=col+2).value = pay_num
        
        self.save()
        return True
    
    def _find_empty_cell(self, ws):
        """البحث عن خلية فارغة لإضافة مستند"""
        for i in range(6):  # الأقسام الستة
            col_start = 1 + (i * 3)
            for row in range(10, 33):
                if not ws.cell(row=row, column=col_start).value:
                    return (row, col_start)
        return None
    
    def search_documents(self, search_type, search_value):
        """البحث في المستندات"""
        results = []
        
        for sheet_name in self.wb.sheetnames:
            if sheet_name != 'التقارير':
                ws = self.wb[sheet_name]
                section_results = self._search_in_sheet(ws, search_type, search_value)
                results.extend([(sheet_name, *r) for r in section_results])
        
        return results
    
    def create_report(self):
        """إنشاء تقرير المجاميع"""
        if 'التقارير' in self.wb.sheetnames:
            self.wb.remove(self.wb['التقارير'])
            
        ws = self.wb.create_sheet('التقارير')
        ws.sheet_properties.rightToLeft = True
        
        # إعداد العناوين
        ws['A1'] = 'المادة'
        ws['B1'] = 'المبلغ'
        for cell in [ws['A1'], ws['B1']]:
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
        
        # إضافة الحسابات
        row = 2
        for sheet_name in self.wb.sheetnames:
            if sheet_name != 'التقارير':
                ws[f'A{row}'] = sheet_name
                ws[f'B{row}'] = f'=\'{sheet_name}\'!A33'
                row += 1
        
        # إضافة المجموع
        ws[f'A{row}'] = 'المجموع الإجمالي'
        ws[f'B{row}'] = f'=SUM(B2:B{row-1})'
        
        self.save()
    def save(self, path=None):
        """حفظ الملف"""
        try:
            save_path = path or self.file_path
            self.file_path = save_path
            self.wb.save(save_path)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الملف: {str(e)}")
            return False
