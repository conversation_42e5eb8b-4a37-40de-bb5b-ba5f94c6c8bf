import openpyxl
from openpyxl.styles import Font, Align<PERSON>, PatternFill, Border, Side
from datetime import datetime
import os
from tkinter import messagebox

class ExcelManager:
    def __init__(self):
        self.current_file = None
        self.workbook = None
        self.create_new_workbook()
    
    def create_new_workbook(self):
        """إنشاء ملف Excel جديد"""
        self.workbook = openpyxl.Workbook()
        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])
        self.current_file = "accounting_system.xlsx"
        self.save_workbook()
    
    def save_workbook(self):
        """حفظ الملف"""
        try:
            self.workbook.save(self.current_file)
            return True
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")
            return False
    
    def create_account_sheet(self, account_num, account_name, initial_balance=0):
        """إنشاء صفحة حساب جديدة"""
        try:
            sheet_name = f"{account_num}-{account_name}"
            
            # التحقق من عدم وجود الحساب
            if sheet_name in self.workbook.sheetnames:
                messagebox.showerror("خطأ", "هذا الحساب موجود مسبقاً")
                return False
            
            # إنشاء الورقة
            ws = self.workbook.create_sheet(sheet_name)
            
            # إعداد الترويسة
            ws.merge_cells('A1:R1')
            ws['A1'] = "المملكة الأردنية الهاشمية"
            ws['A1'].font = Font(size=14, bold=True)
            ws['A1'].alignment = Alignment(horizontal='center')
            
            ws.merge_cells('A2:R2')
            ws['A2'] = "وزارة / الدائرة : وزارة الصحة"
            ws['A2'].alignment = Alignment(horizontal='center')
            
            # إعداد الأقسام
            self._setup_sections(ws, initial_balance)
            
            # حفظ التغييرات
            if self.save_workbook():
                messagebox.showinfo("نجاح", "تم إنشاء الحساب بنجاح")
                return True
            return False
            
        except Exception as e:
            messagebox.showerror("خطأ", str(e))
            return False
    
    def _setup_sections(self, ws, initial_balance):
        """إعداد أقسام الصفحة"""
        for i in range(6):
            col = chr(65 + (i * 3))  # A, D, G, J, M, P
            
            # العناوين
            ws[f'{col}8'] = "فلس/دينار"
            ws[f'{chr(ord(col) + 1)}8'] = "مستند الصرف"
            ws[f'{chr(ord(col) + 2)}8'] = "رقم التأدية"
            
            # تنسيق العناوين
            for j in range(3):
                cell = ws[f'{chr(ord(col) + j)}8']
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # القيم الأولية
            ws[f'{col}9'] = initial_balance if i == 0 else f"=SUM({chr(ord(col)-3)}34)"
            ws[f'{chr(ord(col) + 1)}9'] = "ما قبله"
            
            # صيغ المجموع
            ws[f'{col}33'] = f"=SUM({col}9:{col}32)"
    
    def add_document(self, sheet_name, amount, doc_num, pay_num):
        """إضافة مستند إلى حساب"""
        try:
            ws = self.workbook[sheet_name]
            empty_cell = self._find_empty_cell(ws)
            
            if not empty_cell:
                messagebox.showerror("خطأ", "لا يوجد مكان لإضافة مستند جديد")
                return False
            
            row, col = empty_cell
            ws.cell(row=row, column=col).value = amount
            ws.cell(row=row, column=col+1).value = doc_num
            ws.cell(row=row, column=col+2).value = pay_num
            
            if self.save_workbook():
                messagebox.showinfo("نجاح", "تم إضافة المستند بنجاح")
                return True
            return False
            
        except Exception as e:
            messagebox.showerror("خطأ", str(e))
            return False
    
    def _find_empty_cell(self, ws):
        """البحث عن خلية فارغة"""
        for i in range(6):  # الأقسام الستة
            col = 1 + (i * 3)
            for row in range(10, 33):
                if not ws.cell(row=row, column=col).value:
                    return (row, col)
        return None
