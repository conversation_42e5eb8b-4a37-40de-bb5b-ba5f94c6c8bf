import PyInstaller.__main__
import os
import sys

def build_exe():
    # الحصول على المسار الحالي
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # تحديد مسار الأيقونة
    icon_path = os.path.join(current_dir, 'icon.ico')
    
    # تكوين خيارات البناء
    options = [
        'app.py',  # الملف الرئيسي
        '--onefile',  # إنشاء ملف تنفيذي واحد
        '--windowed',  # تطبيق نافذة (بدون نافذة terminal)
        '--clean',  # تنظيف مجلد البناء قبل البدء
        '--noconfirm',  # عدم طلب التأكيد عند حذف مجلد dist
        f'--distpath={os.path.join(current_dir, "dist")}',  # مسار الإخراج
        f'--workpath={os.path.join(current_dir, "build")}',  # مسار ملفات البناء
        '--add-data=accounting_system.xlsx;.',  # تضمين ملف Excel الافتراضي
        '--name=AccountingSystem'  # اسم الملف التنفيذي
    ]
    
    # إضافة الأيقونة إذا كانت موجودة
    if os.path.exists(icon_path):
        options.append(f'--icon={icon_path}')
    
    # بناء التطبيق
    PyInstaller.__main__.run(options)

if __name__ == '__main__':
    build_exe()
