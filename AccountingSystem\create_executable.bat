@echo off
chcp 65001 >nul
title Create Executable - Accounting System

echo ========================================
echo    Create Standalone Executable
echo    Accounting System
echo ========================================
echo.

echo This script will create a standalone executable
echo that can run on any Windows computer without Python.
echo.

REM Use the specific Python path
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists
if exist %PYTHON_PATH% (
    echo Python found at: %PYTHON_PATH%
    echo.
    
    echo Step 1: Verifying distribution readiness...
    echo ==========================================
    %PYTHON_PATH% verify_distribution.py
    
    if %errorlevel% neq 0 (
        echo.
        echo Verification failed. Please fix the issues above.
        pause
        exit /b 1
    )
    
    echo.
    echo Step 2: Setting up build environment...
    echo ======================================
    call setup_build_environment.bat
    
    if %errorlevel% neq 0 (
        echo.
        echo Environment setup failed.
        pause
        exit /b 1
    )
    
    echo.
    echo Step 3: Building executable...
    echo =============================
    call build_executable.bat
    
    if %errorlevel% equ 0 (
        echo.
        echo ========================================
        echo    SUCCESS!
        echo ========================================
        echo.
        echo Standalone executable created successfully!
        echo.
        echo Location: dist\نظام_إدارة_المستندات_المحاسبية.exe
        echo Launcher: dist\تشغيل_النظام.bat
        echo.
        echo You can now copy the 'dist' folder to any Windows computer
        echo and run the application without installing Python.
        echo.
        echo File size: ~50-80 MB
        echo Requirements: Windows 10/11 only
        echo.
        
        REM Show file info if possible
        if exist "dist\نظام_إدارة_المستندات_المحاسبية.exe" (
            echo File created: dist\نظام_إدارة_المستندات_المحاسبية.exe
            dir "dist\نظام_إدارة_المستندات_المحاسبية.exe" | find "نظام_إدارة_المستندات_المحاسبية.exe"
        )
        
        echo.
        echo Would you like to test the executable now? (Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            echo.
            echo Testing executable...
            cd dist
            "نظام_إدارة_المستندات_المحاسبية.exe"
            cd ..
        )
        
    ) else (
        echo.
        echo ========================================
        echo    BUILD FAILED!
        echo ========================================
        echo.
        echo The executable build process failed.
        echo Please check the error messages above.
        echo.
    )
    
) else (
    echo ERROR: Python not found at expected location
    echo Expected: %PYTHON_PATH%
    echo.
    echo Please install Python 3.7+ from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    echo Or update the PYTHON_PATH variable in this script.
    pause
    exit /b 1
)

echo.
echo Press any key to exit...
pause >nul
