#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف تنفيذي للنظام المحاسبي
"""

import os
import sys
import shutil
import subprocess

def check_pyinstaller():
    """فحص وجود PyInstaller"""
    try:
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ PyInstaller متوفر: {result.stdout.strip()}")
            return True
        else:
            print("❌ PyInstaller غير متوفر")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        print("❌ PyInstaller غير مثبت")
        return False

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("📦 جاري تثبيت PyInstaller...")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], 
                              capture_output=True, text=True, timeout=120)
        if result.returncode == 0:
            print("✅ تم تثبيت PyInstaller بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت PyInstaller: {result.stderr}")
            return False
    except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
        print(f"❌ خطأ في تثبيت PyInstaller: {e}")
        return False

def create_executable():
    """إنشاء الملف التنفيذي"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    print("🔨 بدء إنشاء الملف التنفيذي...")
    
    # تنظيف المجلدات السابقة
    for folder in ['build', 'dist']:
        folder_path = os.path.join(current_dir, folder)
        if os.path.exists(folder_path):
            shutil.rmtree(folder_path)
            print(f"🗑️ تم حذف مجلد {folder}")
    
    # تكوين أوامر PyInstaller
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=نظام_المحاسبة_وزارة_الصحة',
        '--hidden-import=openpyxl',
        '--hidden-import=openpyxl.styles',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.filedialog',
        '--collect-all=openpyxl',
        '--optimize=2',
        'main.py'
    ]
    
    try:
        print("📦 جاري تجميع الملف التنفيذي...")
        result = subprocess.run(cmd, cwd=current_dir, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ تم إنشاء الملف التنفيذي بنجاح!")
            
            # نسخ ملف Excel الافتراضي
            dist_dir = os.path.join(current_dir, 'dist')
            excel_source = os.path.join(current_dir, 'accounting_system.xlsx')
            excel_dest = os.path.join(dist_dir, 'accounting_system.xlsx')
            
            if os.path.exists(excel_source):
                shutil.copy2(excel_source, excel_dest)
                print("📊 تم نسخ ملف Excel الافتراضي")
            
            print(f"📁 الملف التنفيذي موجود في: {dist_dir}")
            return True
        else:
            print(f"❌ فشل في إنشاء الملف التنفيذي:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ انتهت مهلة إنشاء الملف التنفيذي")
        return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف التنفيذي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء ملف تنفيذي لنظام إدارة المستندات المحاسبية")
    print("=" * 60)
    
    # فحص PyInstaller
    if not check_pyinstaller():
        print("📦 محاولة تثبيت PyInstaller...")
        if not install_pyinstaller():
            print("❌ فشل في تثبيت PyInstaller")
            print("يرجى تثبيته يدوياً: pip install pyinstaller")
            return False
    
    # إنشاء الملف التنفيذي
    if create_executable():
        print("\n🎉 تم إنشاء الملف التنفيذي بنجاح!")
        print("✅ يمكنك الآن تشغيل النظام من مجلد dist")
        return True
    else:
        print("\n❌ فشل في إنشاء الملف التنفيذي")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
