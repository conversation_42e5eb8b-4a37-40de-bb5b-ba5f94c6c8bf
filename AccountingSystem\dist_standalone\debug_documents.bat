@echo off
chcp 65001 >nul
title Debug Documents - Accounting System

echo ========================================
echo    Debug Documents Issue
echo    Accounting System
echo ========================================
echo.

echo Running advanced document debugging...
echo.

REM Use the specific Python path
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists
if exist %PYTHON_PATH% (
    echo Python found, running debug...
    echo.
    
    REM Run the debug script
    %PYTHON_PATH% debug_documents.py
    
) else (
    echo ERROR: Python not found at expected location
    pause
    exit /b 1
)

echo.
echo Debug completed.
pause
