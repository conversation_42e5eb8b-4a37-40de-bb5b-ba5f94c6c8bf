#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص متقدم لمشكلة إضافة المستندات
"""

import os
import sys

def debug_document_addition():
    """تشخيص مشكلة إضافة المستندات"""
    try:
        print("🔍 تشخيص متقدم لمشكلة إضافة المستندات")
        print("=" * 60)
        
        # استيراد المكتبات
        from excel_manager import ExcelManager
        
        # إنشاء مدير Excel
        excel = ExcelManager()
        print("✅ تم إنشاء ExcelManager")
        
        # حذف ملف الاختبار إن وجد
        test_file = "accounting_system.xlsx"
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
                print("🗑️ تم حذف ملف الاختبار السابق")
            except:
                pass
        
        # إنشاء حساب تجريبي جديد
        account_num = "DEBUG001"
        account_name = "حساب تشخيص المستندات"
        initial_balance = 10000
        
        print(f"\n📝 إنشاء حساب جديد:")
        print(f"   رقم الحساب: {account_num}")
        print(f"   اسم الحساب: {account_name}")
        print(f"   الرصيد الافتتاحي: {initial_balance}")
        
        result = excel.create_account_sheet(account_num, account_name, initial_balance)
        
        if not result:
            print("❌ فشل في إنشاء الحساب")
            return False
        
        print("✅ تم إنشاء الحساب بنجاح")
        
        # تشخيص الحساب المنشأ حديثاً
        sheet_name = f"{account_num}-{account_name}"
        print(f"\n🔍 تشخيص الحساب المنشأ: {sheet_name}")
        excel.diagnose_account(sheet_name)
        
        # محاولة إضافة مستند
        print(f"\n📄 محاولة إضافة مستند:")
        amount = 1500.75
        doc_num = "DEBUG_DOC_001"
        pay_num = "DEBUG_PAY_001"
        
        print(f"   المبلغ: {amount}")
        print(f"   رقم المستند: {doc_num}")
        print(f"   رقم التأدية: {pay_num}")
        
        result = excel.add_document(sheet_name, amount, doc_num, pay_num)
        
        if result:
            print("✅ تم إضافة المستند بنجاح!")
            
            # تشخيص الحساب بعد إضافة المستند
            print(f"\n🔍 تشخيص الحساب بعد إضافة المستند:")
            excel.diagnose_account(sheet_name)
            
            # التحقق من البيانات المحفوظة
            ws = excel.workbook[sheet_name]
            saved_amount = ws['A10'].value
            saved_doc = ws['B10'].value
            saved_pay = ws['C10'].value
            
            print(f"\n📊 البيانات المحفوظة:")
            print(f"   المبلغ المحفوظ: {saved_amount}")
            print(f"   رقم المستند المحفوظ: {saved_doc}")
            print(f"   رقم التأدية المحفوظ: {saved_pay}")
            
            # التحقق من صحة البيانات
            if (saved_amount == amount and 
                str(saved_doc) == str(doc_num) and 
                str(saved_pay) == str(pay_num)):
                print("✅ جميع البيانات محفوظة بشكل صحيح")
                return True
            else:
                print("❌ البيانات المحفوظة غير صحيحة")
                return False
        else:
            print("❌ فشل في إضافة المستند")
            
            # تشخيص إضافي للمشكلة
            print(f"\n🔍 تشخيص إضافي للمشكلة:")
            excel.diagnose_account(sheet_name)
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_documents():
    """اختبار إضافة عدة مستندات"""
    try:
        print("\n" + "=" * 60)
        print("🧪 اختبار إضافة عدة مستندات")
        print("=" * 60)
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # استخدام الحساب الموجود
        sheet_name = "DEBUG001-حساب تشخيص المستندات"
        
        if sheet_name not in excel.workbook.sheetnames:
            print("❌ الحساب التجريبي غير موجود")
            return False
        
        # إضافة عدة مستندات
        documents = [
            (2000, "DOC002", "PAY002"),
            (1500, "DOC003", "PAY003"),
            (3000, "DOC004", "PAY004"),
            (500, "DOC005", "PAY005")
        ]
        
        success_count = 0
        for i, (amount, doc_num, pay_num) in enumerate(documents, 2):
            print(f"\n📄 إضافة المستند {i}:")
            print(f"   المبلغ: {amount}, المستند: {doc_num}, التأدية: {pay_num}")
            
            result = excel.add_document(sheet_name, amount, doc_num, pay_num)
            if result:
                print(f"✅ تم إضافة المستند {i} بنجاح")
                success_count += 1
            else:
                print(f"❌ فشل في إضافة المستند {i}")
                break
        
        print(f"\n📊 النتيجة: تم إضافة {success_count} من أصل {len(documents)} مستندات")
        
        # تشخيص نهائي
        print(f"\n🔍 التشخيص النهائي:")
        excel.diagnose_account(sheet_name)
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المستندات المتعددة: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🚀 بدء التشخيص المتقدم لمشكلة إضافة المستندات")
    
    success_count = 0
    total_tests = 2
    
    # تشخيص إضافة مستند واحد
    if debug_document_addition():
        success_count += 1
    
    # اختبار إضافة عدة مستندات
    if test_multiple_documents():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج التشخيص: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 تم حل مشكلة إضافة المستندات!")
        print("✅ النظام يعمل بشكل صحيح")
    elif success_count > 0:
        print("⚠️ تم حل المشكلة جزئياً")
        print("🔍 قد تحتاج إلى مراجعة إضافية")
    else:
        print("❌ لا تزال هناك مشكلة في إضافة المستندات")
        print("🔧 يرجى مراجعة الكود والتشخيص أعلاه")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
