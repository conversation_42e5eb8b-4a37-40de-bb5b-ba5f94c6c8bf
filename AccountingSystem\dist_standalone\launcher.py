#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل النظام المحاسبي - ملف تشغيل مبسط
"""

import sys
import os

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """تشغيل النظام"""
    try:
        # استيراد tkinter
        import tkinter as tk
        from tkinter import messagebox
        
        # التحقق من openpyxl
        try:
            import openpyxl
        except ImportError:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة")
            return
        
        # استيراد التطبيق
        from app import AccountingApp
        
        # إنشاء النافذة
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة")
        root.geometry("1200x700")
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        
        # تشغيل التطبيق
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للإغلاق...")

if __name__ == "__main__":
    main()
