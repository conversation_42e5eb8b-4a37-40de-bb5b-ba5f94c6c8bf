import tkinter as tk
from tkinter import ttk, messagebox

class ManageAccountsDialog(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("إدارة الحسابات")
        self.excel = excel
        
        # تكوين النافذة
        self.geometry("800x500")
        self.configure(bg='#f0f0f0')
        
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # قائمة الحسابات
        self.create_accounts_list(main_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
        # تحديث القائمة
        self.load_accounts()
    
    def create_accounts_list(self, parent):
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="الحسابات", padding="5")
        list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=5)
        
        # إنشاء جدول الحسابات مع إمكانية التحديد المتعدد
        columns = ('رقم الحساب', 'اسم الحساب', 'الرصيد')
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show='headings', selectmode='extended')
        
        # تعيين العناوين
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=150)
        
        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)
        
        # وضع العناصر في الإطار
        self.accounts_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # تمكين التمدد
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
    
    def create_control_buttons(self, parent):
        # إطار الأزرار
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=1, column=0, pady=10)

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.pack(pady=5)

        ttk.Button(row1_frame, text="عرض تفاصيل الحساب",
                  command=self.view_account_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1_frame, text="تصدير تفاصيل الحساب",
                  command=self.export_account_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1_frame, text="تعديل الحساب",
                  command=self.edit_account).pack(side=tk.LEFT, padx=5)

        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.pack(pady=5)

        ttk.Button(row2_frame, text="حذف الحساب",
                  command=self.delete_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="حذف الحسابات المحددة",
                  command=self.delete_selected_accounts).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="تحديث القائمة",
                  command=self.load_accounts).pack(side=tk.LEFT, padx=5)

        # الصف الثالث من الأزرار
        row3_frame = ttk.Frame(buttons_frame)
        row3_frame.pack(pady=5)

        ttk.Button(row3_frame, text="اختيار الشعار",
                  command=self.select_logo).pack(side=tk.LEFT, padx=5)
        ttk.Button(row3_frame, text="حذف جميع الحسابات",
                  command=self.delete_all_accounts).pack(side=tk.LEFT, padx=5)
        ttk.Button(row3_frame, text="إغلاق",
                  command=self.destroy).pack(side=tk.LEFT, padx=5)
    
    def load_accounts(self):
        """تحميل الحسابات في الجدول"""
        # مسح الجدول
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)
        
        # تحميل الحسابات
        for sheet_name in self.excel.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                try:
                    # استخراج رقم واسم الحساب
                    account_num, account_name = sheet_name.split('-', 1)
                    
                    # الحصول على الرصيد من الصف 31 (المجموع الجديد)
                    ws = self.excel.workbook[sheet_name]
                    balance = ws['A31'].value or 0
                    
                    # إضافة الصف
                    self.accounts_tree.insert('', tk.END, values=(account_num, account_name, balance))
                except:
                    continue

    def view_account_details(self):
        """عرض تفاصيل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لعرض تفاصيله")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # إنشاء نافذة التفاصيل
        details_dialog = AccountDetailsDialog(self, self.excel, sheet_name, account_num, account_name)
        self.wait_window(details_dialog)

    def export_account_details(self):
        """تصدير تفاصيل الحساب المحدد مباشرة"""
        try:
            selection = self.accounts_tree.selection()
            if not selection:
                messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتصدير تفاصيله")
                return

            item = selection[0]
            account_num, account_name, balance = self.accounts_tree.item(item)['values']
            sheet_name = f"{account_num}-{account_name}"

            print(f"🔄 بدء تصدير تفاصيل الحساب: {sheet_name}")  # للتشخيص

            # إنشاء نافذة التفاصيل مؤقتاً للحصول على البيانات
            temp_dialog = AccountDetailsDialog(self, self.excel, sheet_name, account_num, account_name)
            temp_dialog.withdraw()  # إخفاء النافذة

            # انتظار تحميل البيانات
            temp_dialog.update()

            # تصدير التفاصيل مباشرة
            temp_dialog.export_details()

            # إغلاق النافذة المؤقتة
            temp_dialog.destroy()

        except Exception as e:
            print(f"❌ خطأ في تصدير تفاصيل الحساب: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير تفاصيل الحساب:\n{str(e)}")

    def edit_account(self):
        """تعديل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتعديله")
            return
        
        item = selection[0]
        account_num, account_name, _ = self.accounts_tree.item(item)['values']
        
        # إنشاء نافذة التعديل
        dialog = AccountEditDialog(self, self.excel, account_num, account_name)
        self.wait_window(dialog)
        
        # تحديث القائمة
        self.load_accounts()
    
    def delete_account(self):
        """حذف الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لحذفه")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # نافذة تأكيد بسيطة وموثوقة
        confirm_message = (f"هل أنت متأكد من حذف الحساب؟\n\n"
                          f"رقم الحساب: {account_num}\n"
                          f"اسم الحساب: {account_name}\n"
                          f"الرصيد الحالي: {balance}\n\n"
                          f"تحذير: سيتم حذف الحساب نهائياً مع جميع المستندات!")

        if messagebox.askyesno("تأكيد حذف الحساب", confirm_message):
            try:
                print(f"🗑️ حذف الحساب: {sheet_name}")  # للتشخيص

                # التحقق من وجود الحساب
                if sheet_name not in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", f"الحساب '{sheet_name}' غير موجود")
                    return

                # حذف الصفحة
                self.excel.workbook.remove(self.excel.workbook[sheet_name])
                print(f"✅ تم حذف الصفحة من الملف")  # للتشخيص

                # تحديث التقرير الإجمالي تلقائياً
                print(f"📊 تحديث التقرير الإجمالي بعد حذف الحساب...")
                self.excel.create_summary_report()

                # حفظ التغييرات
                if self.excel.save_workbook():
                    print(f"✅ تم حفظ الملف بنجاح")  # للتشخيص

                    # تحديث القائمة
                    self.load_accounts()

                    messagebox.showinfo("نجاح", f"تم حذف الحساب '{account_name}' بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ التغييرات")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء حذف الحساب: {str(e)}"
                print(f"❌ {error_msg}")  # للتشخيص
                messagebox.showerror("خطأ", error_msg)

    def delete_selected_accounts(self):
        """حذف الحسابات المحددة"""
        selections = self.accounts_tree.selection()
        if not selections:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب أو أكثر لحذفها")
            return

        # جمع معلومات الحسابات المحددة
        selected_accounts = []
        for item in selections:
            account_num, account_name, balance = self.accounts_tree.item(item)['values']
            sheet_name = f"{account_num}-{account_name}"
            selected_accounts.append({
                'num': account_num,
                'name': account_name,
                'balance': balance,
                'sheet_name': sheet_name
            })

        # إنشاء رسالة التأكيد
        accounts_list = "\n".join([f"• {acc['num']} - {acc['name']} (الرصيد: {acc['balance']})"
                                  for acc in selected_accounts])

        confirm_message = (f"هل أنت متأكد من حذف الحسابات التالية؟\n\n"
                          f"{accounts_list}\n\n"
                          f"عدد الحسابات المحددة: {len(selected_accounts)}\n\n"
                          f"تحذير: سيتم حذف جميع الحسابات نهائياً مع جميع المستندات!")

        if messagebox.askyesno("تأكيد حذف الحسابات المحددة", confirm_message):
            try:
                deleted_count = 0
                failed_accounts = []

                for account in selected_accounts:
                    try:
                        print(f"🗑️ حذف الحساب: {account['sheet_name']}")

                        # التحقق من وجود الحساب
                        if account['sheet_name'] not in self.excel.workbook.sheetnames:
                            failed_accounts.append(f"{account['name']} (غير موجود)")
                            continue

                        # حذف الصفحة
                        self.excel.workbook.remove(self.excel.workbook[account['sheet_name']])
                        deleted_count += 1
                        print(f"✅ تم حذف الحساب: {account['name']}")

                    except Exception as e:
                        failed_accounts.append(f"{account['name']} (خطأ: {str(e)})")
                        print(f"❌ فشل في حذف الحساب {account['name']}: {str(e)}")

                # تحديث التقرير الإجمالي
                if deleted_count > 0:
                    print(f"📊 تحديث التقرير الإجمالي بعد حذف {deleted_count} حساب...")
                    self.excel.create_summary_report()

                    # حفظ التغييرات
                    if self.excel.save_workbook():
                        print(f"✅ تم حفظ الملف بنجاح")

                        # تحديث القائمة
                        self.load_accounts()

                        # رسالة النتيجة
                        result_message = f"تم حذف {deleted_count} حساب بنجاح"
                        if failed_accounts:
                            result_message += f"\n\nفشل في حذف:\n" + "\n".join(failed_accounts)

                        messagebox.showinfo("نتيجة الحذف", result_message)
                    else:
                        messagebox.showerror("خطأ", "فشل في حفظ التغييرات")
                else:
                    messagebox.showwarning("تنبيه", "لم يتم حذف أي حساب")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء حذف الحسابات: {str(e)}"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ", error_msg)

    def delete_all_accounts(self):
        """حذف جميع الحسابات"""
        # جمع جميع الحسابات
        all_accounts = []
        for sheet_name in self.excel.workbook.sheetnames:
            if sheet_name not in ['التقرير الإجمالي', 'التقارير', 'تقرير المستندات']:
                try:
                    if '-' in sheet_name:
                        account_num, account_name = sheet_name.split('-', 1)
                        all_accounts.append({
                            'num': account_num,
                            'name': account_name,
                            'sheet_name': sheet_name
                        })
                except:
                    continue

        if not all_accounts:
            messagebox.showinfo("معلومات", "لا توجد حسابات لحذفها")
            return

        # إنشاء نافذة تأكيد خاصة لحذف جميع الحسابات
        confirm_dialog = DeleteAllAccountsDialog(self, all_accounts)
        self.wait_window(confirm_dialog)

        if confirm_dialog.confirmed:
            try:
                deleted_count = 0
                failed_accounts = []

                # حذف جميع الحسابات
                for account in all_accounts:
                    try:
                        print(f"🗑️ حذف الحساب: {account['sheet_name']}")

                        # التحقق من وجود الحساب
                        if account['sheet_name'] not in self.excel.workbook.sheetnames:
                            failed_accounts.append(f"{account['name']} (غير موجود)")
                            continue

                        # حذف الصفحة
                        self.excel.workbook.remove(self.excel.workbook[account['sheet_name']])
                        deleted_count += 1
                        print(f"✅ تم حذف الحساب: {account['name']}")

                    except Exception as e:
                        failed_accounts.append(f"{account['name']} (خطأ: {str(e)})")
                        print(f"❌ فشل في حذف الحساب {account['name']}: {str(e)}")

                # تحديث التقرير الإجمالي (سيكون فارغاً)
                if deleted_count > 0:
                    print(f"📊 تحديث التقرير الإجمالي بعد حذف جميع الحسابات...")
                    self.excel.create_summary_report()

                    # حفظ التغييرات
                    if self.excel.save_workbook():
                        print(f"✅ تم حفظ الملف بنجاح")

                        # تحديث القائمة
                        self.load_accounts()

                        # رسالة النتيجة
                        result_message = f"تم حذف {deleted_count} حساب من أصل {len(all_accounts)}"
                        if failed_accounts:
                            result_message += f"\n\nفشل في حذف:\n" + "\n".join(failed_accounts)

                        messagebox.showinfo("نتيجة حذف جميع الحسابات", result_message)
                    else:
                        messagebox.showerror("خطأ", "فشل في حفظ التغييرات")
                else:
                    messagebox.showwarning("تنبيه", "لم يتم حذف أي حساب")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء حذف جميع الحسابات: {str(e)}"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ", error_msg)

    def select_logo(self):
        """اختيار الشعار وإضافته لجميع الحسابات"""
        try:
            from tkinter import filedialog
            import os

            # اختيار ملف الصورة
            logo_path = filedialog.askopenfilename(
                title="اختيار الشعار",
                filetypes=[
                    ("ملفات الصور", "*.png *.jpg *.jpeg *.gif *.bmp"),
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg *.jpeg"),
                    ("جميع الملفات", "*.*")
                ]
            )

            if not logo_path:
                return

            # التحقق من وجود الملف
            if not os.path.exists(logo_path):
                messagebox.showerror("خطأ", "الملف المحدد غير موجود")
                return

            # إنشاء نافذة تأكيد مع معاينة
            confirm_dialog = LogoConfirmDialog(self, logo_path, self.excel)
            self.wait_window(confirm_dialog)

            if confirm_dialog.confirmed:
                # تطبيق الشعار على جميع الحسابات
                self.apply_logo_to_all_accounts(logo_path)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء اختيار الشعار: {str(e)}")

    def apply_logo_to_all_accounts(self, logo_path):
        """تطبيق الشعار على جميع الحسابات"""
        try:
            from openpyxl.drawing import Image
            import os

            # جمع جميع الحسابات
            account_sheets = []
            for sheet_name in self.excel.workbook.sheetnames:
                if sheet_name not in ['التقرير الإجمالي', 'التقارير', 'تقرير المستندات']:
                    account_sheets.append(sheet_name)

            if not account_sheets:
                messagebox.showinfo("معلومات", "لا توجد حسابات لإضافة الشعار إليها")
                return

            # إنشاء نافذة تقدم
            progress_dialog = LogoProgressDialog(self, len(account_sheets))

            success_count = 0
            failed_accounts = []

            for i, sheet_name in enumerate(account_sheets):
                try:
                    # تحديث نافذة التقدم
                    progress_dialog.update_progress(i + 1, sheet_name)
                    progress_dialog.update()

                    ws = self.excel.workbook[sheet_name]

                    # حذف الشعار القديم إن وجد
                    self.remove_existing_logo(ws)

                    # إضافة الشعار الجديد
                    img = Image(logo_path)

                    # تحديد حجم الشعار (مناسب للخلايا A1:D4)
                    img.width = 120  # عرض مناسب
                    img.height = 80  # ارتفاع مناسب

                    # وضع الشعار في الموضع المحدد
                    img.anchor = 'A1'

                    # إضافة الشعار إلى الورقة
                    ws.add_image(img)

                    success_count += 1
                    print(f"✅ تم إضافة الشعار للحساب: {sheet_name}")

                except Exception as e:
                    failed_accounts.append(f"{sheet_name}: {str(e)}")
                    print(f"❌ فشل في إضافة الشعار للحساب {sheet_name}: {str(e)}")

            # إغلاق نافذة التقدم
            progress_dialog.destroy()

            # حفظ التغييرات
            if success_count > 0:
                if self.excel.save_workbook():
                    # رسالة النتيجة
                    result_message = f"تم إضافة الشعار لـ {success_count} حساب من أصل {len(account_sheets)}"
                    if failed_accounts:
                        result_message += f"\n\nفشل في:\n" + "\n".join(failed_accounts[:5])
                        if len(failed_accounts) > 5:
                            result_message += f"\n... و {len(failed_accounts) - 5} حساب آخر"

                    messagebox.showinfo("نتيجة إضافة الشعار", result_message)
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ التغييرات")
            else:
                messagebox.showwarning("تنبيه", "لم يتم إضافة الشعار لأي حساب")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تطبيق الشعار: {str(e)}")

    def remove_existing_logo(self, ws):
        """حذف الشعار الموجود من الورقة"""
        try:
            # حذف جميع الصور الموجودة
            if hasattr(ws, '_images'):
                ws._images.clear()

            # حذف الصور من مجموعة الرسوم
            if hasattr(ws, 'drawings'):
                ws.drawings.clear()

        except Exception as e:
            print(f"تحذير: لم يتم حذف الشعار القديم: {str(e)}")

class AccountEditDialog(tk.Toplevel):
    def __init__(self, parent, excel, account_num, account_name):
        super().__init__(parent)
        self.title("تعديل الحساب")
        self.excel = excel
        self.old_sheet_name = f"{account_num}-{account_name}"
        
        # تكوين النافذة
        self.geometry("400x200")
        
        # حقول الإدخال
        ttk.Label(self, text="رقم الحساب:").grid(row=0, column=0, padx=5, pady=5)
        self.account_num = ttk.Entry(self)
        self.account_num.insert(0, account_num)
        self.account_num.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self, text="اسم الحساب:").grid(row=1, column=0, padx=5, pady=5)
        self.account_name = ttk.Entry(self)
        self.account_name.insert(0, account_name)
        self.account_name.grid(row=1, column=1, padx=5, pady=5)
        
        # أزرار
        ttk.Button(self, text="حفظ",
                  command=self.save_changes).grid(row=2, column=0, columnspan=2, pady=20)
    
    def save_changes(self):
        """حفظ التغييرات على الحساب"""
        try:
            new_sheet_name = f"{self.account_num.get()}-{self.account_name.get()}"
            
            if new_sheet_name != self.old_sheet_name:
                # التحقق من عدم وجود حساب بنفس الاسم
                if new_sheet_name in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", "يوجد حساب بنفس الرقم والاسم")
                    return
                
                # تغيير اسم الصفحة
                sheet = self.excel.workbook[self.old_sheet_name]
                sheet.title = new_sheet_name
                
                # حفظ التغييرات
                if self.excel.save_workbook():
                    messagebox.showinfo("نجاح", "تم تعديل الحساب بنجاح")
                    self.destroy()
            else:
                self.destroy()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل الحساب: {str(e)}")

class LogoConfirmDialog(tk.Toplevel):
    """نافذة تأكيد اختيار الشعار مع معاينة"""
    def __init__(self, parent, logo_path, excel):
        super().__init__(parent)
        self.title("تأكيد اختيار الشعار")
        self.logo_path = logo_path
        self.excel = excel
        self.confirmed = False

        # تكوين النافذة
        self.geometry("500x400")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        import os

        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        title_label = tk.Label(main_frame, text="تأكيد اختيار الشعار",
                              font=("Arial", 16, "bold"),
                              bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(pady=(0, 20))

        # معلومات الملف
        info_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(info_frame, text="معلومات الشعار المحدد:",
                font=("Arial", 12, "bold"), bg='#ffffff').pack(pady=5)

        # اسم الملف
        filename = os.path.basename(self.logo_path)
        tk.Label(info_frame, text=f"اسم الملف: {filename}",
                font=("Arial", 10), bg='#ffffff').pack(anchor=tk.W, padx=10)

        # حجم الملف
        try:
            file_size = os.path.getsize(self.logo_path)
            size_kb = file_size / 1024
            tk.Label(info_frame, text=f"حجم الملف: {size_kb:.1f} KB",
                    font=("Arial", 10), bg='#ffffff').pack(anchor=tk.W, padx=10)
        except:
            pass

        # مسار الملف
        tk.Label(info_frame, text=f"المسار: {self.logo_path}",
                font=("Arial", 9), bg='#ffffff', wraplength=400).pack(anchor=tk.W, padx=10, pady=(0, 5))

        # معاينة الصورة (إذا أمكن)
        preview_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.RAISED, bd=2)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        tk.Label(preview_frame, text="معاينة الشعار:",
                font=("Arial", 12, "bold"), bg='#ffffff').pack(pady=5)

        try:
            from PIL import Image, ImageTk

            # فتح الصورة وتغيير حجمها للمعاينة
            pil_image = Image.open(self.logo_path)
            pil_image.thumbnail((200, 150), Image.Resampling.LANCZOS)

            # تحويل للعرض في tkinter
            photo = ImageTk.PhotoImage(pil_image)

            # عرض الصورة
            image_label = tk.Label(preview_frame, image=photo, bg='#ffffff')
            image_label.image = photo  # الاحتفاظ بمرجع
            image_label.pack(pady=10)

        except Exception as e:
            # في حالة فشل المعاينة
            tk.Label(preview_frame, text="لا يمكن معاينة الصورة",
                    font=("Arial", 10), bg='#ffffff', fg='#7f8c8d').pack(pady=20)

        # معلومات التطبيق
        info_text = ("سيتم إضافة هذا الشعار لجميع الحسابات الموجودة.\n"
                    "سيظهر الشعار في الزاوية اليسرى العلوية من كل صفحة حساب.")
        info_label = tk.Label(main_frame, text=info_text,
                             font=("Arial", 10), bg='#f0f0f0', fg='#34495e',
                             wraplength=400, justify=tk.CENTER)
        info_label.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack()

        # زر التأكيد
        confirm_btn = tk.Button(buttons_frame, text="تطبيق الشعار",
                               command=self.confirm_logo,
                               bg='#27ae60', fg='white',
                               font=("Arial", 11, "bold"),
                               padx=20, pady=5)
        confirm_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء",
                              command=self.cancel_logo,
                              bg='#e74c3c', fg='white',
                              font=("Arial", 11),
                              padx=20, pady=5)
        cancel_btn.pack(side=tk.LEFT)

        # ربط مفتاح Escape بالإلغاء
        self.bind('<Escape>', lambda e: self.cancel_logo())

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def confirm_logo(self):
        """تأكيد اختيار الشعار"""
        self.confirmed = True
        self.destroy()

    def cancel_logo(self):
        """إلغاء اختيار الشعار"""
        self.confirmed = False
        self.destroy()

class LogoProgressDialog(tk.Toplevel):
    """نافذة عرض تقدم إضافة الشعار"""
    def __init__(self, parent, total_accounts):
        super().__init__(parent)
        self.title("إضافة الشعار")
        self.total_accounts = total_accounts

        # تكوين النافذة
        self.geometry("400x150")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        title_label = tk.Label(main_frame, text="جاري إضافة الشعار...",
                              font=("Arial", 14, "bold"),
                              bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(pady=(0, 10))

        # شريط التقدم
        from tkinter import ttk
        self.progress = ttk.Progressbar(main_frame, length=300, mode='determinate')
        self.progress.pack(pady=10)

        # نص التقدم
        self.progress_label = tk.Label(main_frame, text="بدء العملية...",
                                      font=("Arial", 10),
                                      bg='#f0f0f0', fg='#7f8c8d')
        self.progress_label.pack()

    def update_progress(self, current, account_name):
        """تحديث شريط التقدم"""
        progress_percent = (current / self.total_accounts) * 100
        self.progress['value'] = progress_percent

        self.progress_label.config(text=f"معالجة الحساب {current} من {self.total_accounts}: {account_name}")

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

class DeleteAllAccountsDialog(tk.Toplevel):
    """نافذة تأكيد حذف جميع الحسابات"""
    def __init__(self, parent, all_accounts):
        super().__init__(parent)
        self.title("تأكيد حذف جميع الحسابات")
        self.all_accounts = all_accounts
        self.confirmed = False

        # تكوين النافذة
        self.geometry("600x400")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # أيقونة تحذير كبيرة
        warning_label = tk.Label(main_frame, text="⚠️", font=("Arial", 60),
                                bg='#f0f0f0', fg='#e74c3c')
        warning_label.pack(pady=(0, 20))

        # عنوان التحذير
        title_label = tk.Label(main_frame, text="تحذير خطير: حذف جميع الحسابات",
                              font=("Arial", 18, "bold"),
                              bg='#f0f0f0', fg='#e74c3c')
        title_label.pack(pady=(0, 15))

        # معلومات الحسابات
        info_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        tk.Label(info_frame, text=f"سيتم حذف {len(self.all_accounts)} حساب:",
                font=("Arial", 14, "bold"), bg='#ffffff', fg='#e74c3c').pack(pady=10)

        # قائمة الحسابات
        accounts_frame = tk.Frame(info_frame, bg='#ffffff')
        accounts_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # إنشاء قائمة قابلة للتمرير
        canvas = tk.Canvas(accounts_frame, bg='#ffffff', height=150)
        scrollbar = tk.Scrollbar(accounts_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#ffffff')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إضافة الحسابات إلى القائمة
        for i, account in enumerate(self.all_accounts):
            account_text = f"{i+1}. {account['num']} - {account['name']}"
            tk.Label(scrollable_frame, text=account_text, font=("Arial", 10),
                    bg='#ffffff', anchor='w').pack(fill=tk.X, padx=5, pady=1)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # رسالة التحذير النهائية
        warning_text = ("هذا الإجراء سيحذف جميع الحسابات نهائياً مع جميع المستندات والبيانات!\n"
                       "لا يمكن التراجع عن هذا الإجراء!\n"
                       "تأكد من أنك قمت بعمل نسخة احتياطية من البيانات.")
        warning_label = tk.Label(main_frame, text=warning_text,
                                font=("Arial", 12, "bold"), bg='#f0f0f0', fg='#e74c3c',
                                wraplength=500, justify=tk.CENTER)
        warning_label.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack()

        # زر الحذف
        delete_btn = tk.Button(buttons_frame, text="حذف جميع الحسابات",
                              command=self.confirm_delete,
                              bg='#e74c3c', fg='white',
                              font=("Arial", 12, "bold"),
                              padx=30, pady=8)
        delete_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء",
                              command=self.cancel_delete,
                              bg='#74b9ff', fg='white',
                              font=("Arial", 12),
                              padx=30, pady=8)
        cancel_btn.pack(side=tk.LEFT)

        # ربط مفتاح Escape بالإلغاء
        self.bind('<Escape>', lambda e: self.cancel_delete())

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def confirm_delete(self):
        """تأكيد الحذف"""
        # تأكيد إضافي
        final_confirm = messagebox.askyesno(
            "تأكيد نهائي",
            f"هل أنت متأكد تماماً من حذف جميع الـ {len(self.all_accounts)} حساب؟\n\n"
            "هذا الإجراء لا يمكن التراجع عنه!",
            icon='warning'
        )

        if final_confirm:
            self.confirmed = True
            self.destroy()

    def cancel_delete(self):
        """إلغاء الحذف"""
        self.confirmed = False
        self.destroy()

class DeleteConfirmDialog(tk.Toplevel):
    """نافذة تأكيد حذف الحساب"""
    def __init__(self, parent, account_num, account_name, balance, sheet_name):
        super().__init__(parent)
        self.title("تأكيد حذف الحساب")
        self.account_num = account_num
        self.account_name = account_name
        self.balance = balance
        self.sheet_name = sheet_name
        self.confirmed = False

        # تكوين النافذة
        self.geometry("500x300")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

        # توسيط النافذة
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # أيقونة تحذير
        warning_label = tk.Label(main_frame, text="⚠️", font=("Arial", 48),
                                bg='#f0f0f0', fg='#ff6b6b')
        warning_label.pack(pady=(0, 20))

        # عنوان التحذير
        title_label = tk.Label(main_frame, text="تحذير: حذف الحساب",
                              font=("Arial", 16, "bold"),
                              bg='#f0f0f0', fg='#d63031')
        title_label.pack(pady=(0, 10))

        # معلومات الحساب
        info_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(info_frame, text="معلومات الحساب المراد حذفه:",
                font=("Arial", 12, "bold"), bg='#ffffff').pack(pady=5)

        tk.Label(info_frame, text=f"رقم الحساب: {self.account_num}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"اسم الحساب: {self.account_name}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"الرصيد الحالي: {self.balance}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10, pady=(0, 5))

        # رسالة التحذير
        warning_text = ("سيتم حذف الحساب نهائياً مع جميع المستندات والبيانات المرتبطة به.\n"
                       "هذا الإجراء لا يمكن التراجع عنه!")
        warning_label = tk.Label(main_frame, text=warning_text,
                                font=("Arial", 11), bg='#f0f0f0', fg='#d63031',
                                wraplength=400, justify=tk.CENTER)
        warning_label.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack()

        # زر الحذف
        delete_btn = tk.Button(buttons_frame, text="حذف الحساب",
                              command=self.confirm_delete,
                              bg='#d63031', fg='white',
                              font=("Arial", 11, "bold"),
                              padx=20, pady=5)
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء",
                              command=self.cancel_delete,
                              bg='#74b9ff', fg='white',
                              font=("Arial", 11),
                              padx=20, pady=5)
        cancel_btn.pack(side=tk.LEFT)

        # ربط مفتاح Escape بالإلغاء
        self.bind('<Escape>', lambda e: self.cancel_delete())

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def confirm_delete(self):
        """تأكيد الحذف"""
        self.confirmed = True
        self.destroy()

    def cancel_delete(self):
        """إلغاء الحذف"""
        self.confirmed = False
        self.destroy()

class AccountDetailsDialog(tk.Toplevel):
    """نافذة عرض تفاصيل الحساب"""
    def __init__(self, parent, excel, sheet_name, account_num, account_name):
        super().__init__(parent)
        self.title(f"تفاصيل الحساب: {account_name}")
        self.excel = excel
        self.sheet_name = sheet_name
        self.account_num = account_num
        self.account_name = account_name

        # تكوين النافذة
        self.geometry("700x500")
        self.configure(bg='#f0f0f0')

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.load_account_data()

        # توسيط النافذة
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات الحساب الأساسية
        info_frame = tk.LabelFrame(main_frame, text="معلومات الحساب",
                                  font=("Arial", 12, "bold"), bg='#f0f0f0')
        info_frame.pack(fill=tk.X, pady=(0, 20))

        # رقم الحساب
        tk.Label(info_frame, text=f"رقم الحساب: {self.account_num}",
                font=("Arial", 11), bg='#f0f0f0').pack(anchor=tk.W, padx=10, pady=5)

        # اسم الحساب
        tk.Label(info_frame, text=f"اسم الحساب: {self.account_name}",
                font=("Arial", 11), bg='#f0f0f0').pack(anchor=tk.W, padx=10, pady=5)

        # الرصيد الافتتاحي
        self.opening_balance_label = tk.Label(info_frame, text="الرصيد الافتتاحي: جاري التحميل...",
                                            font=("Arial", 11), bg='#f0f0f0')
        self.opening_balance_label.pack(anchor=tk.W, padx=10, pady=5)

        # الرصيد الحالي
        self.current_balance_label = tk.Label(info_frame, text="الرصيد الحالي: جاري التحميل...",
                                            font=("Arial", 11, "bold"), bg='#f0f0f0')
        self.current_balance_label.pack(anchor=tk.W, padx=10, pady=5)

        # إحصائيات المستندات
        stats_frame = tk.LabelFrame(main_frame, text="إحصائيات المستندات",
                                   font=("Arial", 12, "bold"), bg='#f0f0f0')
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        self.documents_count_label = tk.Label(stats_frame, text="عدد المستندات: جاري التحميل...",
                                            font=("Arial", 11), bg='#f0f0f0')
        self.documents_count_label.pack(anchor=tk.W, padx=10, pady=5)

        self.total_amount_label = tk.Label(stats_frame, text="إجمالي المبالغ: جاري التحميل...",
                                         font=("Arial", 11), bg='#f0f0f0')
        self.total_amount_label.pack(anchor=tk.W, padx=10, pady=5)

        # قائمة المستندات
        documents_frame = tk.LabelFrame(main_frame, text="المستندات الأخيرة",
                                       font=("Arial", 12, "bold"), bg='#f0f0f0')
        documents_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # جدول المستندات
        columns = ('المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم')
        self.documents_tree = ttk.Treeview(documents_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.documents_tree.heading(col, text=col)
            self.documents_tree.column(col, width=120)

        # شريط التمرير للمستندات
        docs_scrollbar = ttk.Scrollbar(documents_frame, orient=tk.VERTICAL, command=self.documents_tree.yview)
        self.documents_tree.configure(yscrollcommand=docs_scrollbar.set)

        self.documents_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        docs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack(pady=10)

        # زر تصدير التفاصيل
        export_btn = tk.Button(buttons_frame, text="تصدير التفاصيل",
                              command=self.export_details,
                              bg='#00b894', fg='white', font=("Arial", 11),
                              padx=20, pady=5)
        export_btn.pack(side=tk.LEFT, padx=5)

        # زر طباعة التقرير
        print_btn = tk.Button(buttons_frame, text="طباعة التقرير",
                             command=self.print_report,
                             bg='#6c5ce7', fg='white', font=("Arial", 11),
                             padx=20, pady=5)
        print_btn.pack(side=tk.LEFT, padx=5)

        # زر الإغلاق
        close_btn = tk.Button(buttons_frame, text="إغلاق", command=self.destroy,
                             bg='#74b9ff', fg='white', font=("Arial", 11),
                             padx=20, pady=5)
        close_btn.pack(side=tk.LEFT, padx=5)

    def load_account_data(self):
        """تحميل بيانات الحساب"""
        try:
            if self.sheet_name not in self.excel.workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{self.sheet_name}' غير موجود")
                self.destroy()
                return

            ws = self.excel.workbook[self.sheet_name]

            # الرصيد الافتتاحي (من الخلية A9)
            opening_balance = ws['A9'].value or 0
            self.opening_balance_label.config(text=f"الرصيد الافتتاحي: {opening_balance:,.2f}")

            # حساب الرصيد الحالي وإحصائيات المستندات
            documents_count = 0
            total_amount = 0
            documents_list = []

            # فحص جميع الأقسام الستة
            for section in range(6):
                col_start = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                for row in range(10, 32):  # صفوف المستندات
                    amount = ws.cell(row=row, column=col_start).value
                    doc_num = ws.cell(row=row, column=col_start+1).value
                    pay_num = ws.cell(row=row, column=col_start+2).value

                    if amount and doc_num and pay_num:
                        documents_count += 1
                        total_amount += float(amount)
                        documents_list.append((amount, doc_num, pay_num, f"القسم {section + 1}"))

            # الرصيد الحالي
            current_balance = opening_balance + total_amount
            self.current_balance_label.config(text=f"الرصيد الحالي: {current_balance:,.2f}")

            # إحصائيات المستندات
            self.documents_count_label.config(text=f"عدد المستندات: {documents_count}")
            self.total_amount_label.config(text=f"إجمالي المبالغ: {total_amount:,.2f}")

            # تحميل المستندات في الجدول (آخر 20 مستند)
            for doc in documents_list[-20:]:  # آخر 20 مستند
                self.documents_tree.insert('', tk.END, values=doc)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الحساب: {str(e)}")

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def export_details(self):
        """تصدير تفاصيل الحساب إلى ملف Excel"""
        try:
            from tkinter import filedialog, messagebox
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from datetime import datetime

            print("🔄 بدء عملية التصدير...")  # للتشخيص

            # اختيار مكان الحفظ
            safe_account_name = "".join(c for c in self.account_name if c.isalnum() or c in (' ', '-', '_')).strip()
            filename = filedialog.asksaveasfilename(
                title="حفظ تفاصيل الحساب",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname=f"تفاصيل_الحساب_{safe_account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not filename:
                print("❌ تم إلغاء عملية التصدير")
                return

            print(f"📁 مسار الحفظ: {filename}")  # للتشخيص

            # إنشاء ملف Excel جديد
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = f"تفاصيل {safe_account_name}"

            # تعيين اتجاه الصفحة من اليمين إلى اليسار
            ws.sheet_properties.rightToLeft = True
            ws.sheet_view.rightToLeft = True

            print("✅ تم إنشاء ملف Excel وتعيين الاتجاه")  # للتشخيص

            # الترويسة
            ws.merge_cells('A1:F1')
            ws['A1'] = "المملكة الأردنية الهاشمية - وزارة الصحة"
            ws['A1'].font = Font(bold=True, size=14)
            ws['A1'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A2:F2')
            ws['A2'] = f"تفاصيل الحساب: {self.account_name}"
            ws['A2'].font = Font(bold=True, size=12)
            ws['A2'].alignment = Alignment(horizontal='center')

            ws.merge_cells('A3:F3')
            ws['A3'] = f"تاريخ التصدير: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
            ws['A3'].font = Font(size=10)
            ws['A3'].alignment = Alignment(horizontal='center')

            # معلومات الحساب
            row = 5
            ws[f'A{row}'] = "رقم الحساب:"
            ws[f'B{row}'] = self.account_num
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "اسم الحساب:"
            ws[f'B{row}'] = self.account_name
            ws[f'A{row}'].font = Font(bold=True)

            # الحصول على البيانات من النافذة الحالية مع معالجة الأخطاء
            try:
                opening_balance = self.opening_balance_label.cget("text").split(": ")[1]
                current_balance = self.current_balance_label.cget("text").split(": ")[1]
                documents_count = self.documents_count_label.cget("text").split(": ")[1]
                total_amount = self.total_amount_label.cget("text").split(": ")[1]
                print("✅ تم الحصول على البيانات من النافذة")  # للتشخيص
            except Exception as data_error:
                print(f"⚠️ خطأ في الحصول على البيانات: {data_error}")
                # قيم افتراضية في حالة الخطأ
                opening_balance = "غير متوفر"
                current_balance = "غير متوفر"
                documents_count = "غير متوفر"
                total_amount = "غير متوفر"

            row += 1
            ws[f'A{row}'] = "الرصيد الافتتاحي:"
            ws[f'B{row}'] = opening_balance
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "الرصيد الحالي:"
            ws[f'B{row}'] = current_balance
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "عدد المستندات:"
            ws[f'B{row}'] = documents_count
            ws[f'A{row}'].font = Font(bold=True)

            row += 1
            ws[f'A{row}'] = "إجمالي المبالغ:"
            ws[f'B{row}'] = total_amount
            ws[f'A{row}'].font = Font(bold=True)

            # عناوين جدول المستندات
            row += 3
            headers = ['المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col)
                cell.value = header
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(all=Side(style='thin'))

            # بيانات المستندات
            try:
                documents_added = 0
                for item in self.documents_tree.get_children():
                    row += 1
                    values = self.documents_tree.item(item)['values']
                    for col, value in enumerate(values, 1):
                        cell = ws.cell(row=row, column=col)
                        cell.value = value
                        cell.border = Border(all=Side(style='thin'))
                        cell.alignment = Alignment(horizontal='center')
                        if col == 1:  # عمود المبلغ
                            try:
                                # محاولة تحويل المبلغ إلى رقم
                                numeric_value = float(str(value).replace(',', ''))
                                cell.value = numeric_value
                                cell.number_format = '#,##0.00'
                            except:
                                cell.value = value  # الاحتفاظ بالقيمة الأصلية إذا فشل التحويل
                    documents_added += 1

                print(f"✅ تم إضافة {documents_added} مستند إلى الملف")  # للتشخيص

            except Exception as docs_error:
                print(f"⚠️ خطأ في إضافة المستندات: {docs_error}")
                # إضافة صف يوضح عدم توفر المستندات
                row += 1
                ws.cell(row=row, column=1).value = "لا توجد مستندات متاحة"
                ws.cell(row=row, column=1).alignment = Alignment(horizontal='center')

            # تنسيق الأعمدة
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 20
            ws.column_dimensions['C'].width = 15
            ws.column_dimensions['D'].width = 15

            # حفظ الملف
            print("💾 جاري حفظ الملف...")
            wb.save(filename)
            print("✅ تم حفظ الملف بنجاح")

            messagebox.showinfo("نجاح", f"تم تصدير تفاصيل الحساب بنجاح إلى:\n{filename}")

        except PermissionError:
            messagebox.showerror("خطأ في الصلاحيات",
                               "لا يمكن حفظ الملف. تأكد من:\n"
                               "• إغلاق الملف إذا كان مفتوحاً في Excel\n"
                               "• وجود صلاحيات الكتابة في المجلد المحدد")
        except Exception as e:
            print(f"❌ خطأ في التصدير: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}\n\nتحقق من:\n• صلاحيات الكتابة\n• إغلاق ملفات Excel المفتوحة")

    def print_report(self):
        """طباعة تقرير الحساب"""
        try:
            import tempfile
            import os
            from datetime import datetime

            # إنشاء ملف HTML للطباعة
            html_content = self.generate_print_html()

            # حفظ الملف المؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة
            import webbrowser
            webbrowser.open(f'file://{temp_file}')

            messagebox.showinfo("طباعة", "تم فتح التقرير في المتصفح للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إعداد الطباعة: {str(e)}")

    def generate_print_html(self):
        """إنشاء محتوى HTML للطباعة"""
        from datetime import datetime

        # الحصول على البيانات
        opening_balance = self.opening_balance_label.cget("text").split(": ")[1]
        current_balance = self.current_balance_label.cget("text").split(": ")[1]
        documents_count = self.documents_count_label.cget("text").split(": ")[1]
        total_amount = self.total_amount_label.cget("text").split(": ")[1]

        # بناء جدول المستندات
        documents_rows = ""
        for item in self.documents_tree.get_children():
            values = self.documents_tree.item(item)['values']
            documents_rows += f"""
            <tr>
                <td>{values[0]}</td>
                <td>{values[1]}</td>
                <td>{values[2]}</td>
                <td>{values[3]}</td>
            </tr>
            """

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تفاصيل الحساب - {self.account_name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .info-table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                .info-table th {{ background-color: #f2f2f2; font-weight: bold; }}
                .documents-table {{ width: 100%; border-collapse: collapse; }}
                .documents-table th, .documents-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .documents-table th {{ background-color: #366092; color: white; }}
                @media print {{
                    body {{ margin: 0; }}
                    .no-print {{ display: none; }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>المملكة الأردنية الهاشمية</h1>
                <h2>وزارة الصحة</h2>
                <h3>تفاصيل الحساب: {self.account_name}</h3>
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y/%m/%d %H:%M')}</p>
            </div>

            <table class="info-table">
                <tr><th>رقم الحساب</th><td>{self.account_num}</td></tr>
                <tr><th>اسم الحساب</th><td>{self.account_name}</td></tr>
                <tr><th>الرصيد الافتتاحي</th><td>{opening_balance}</td></tr>
                <tr><th>الرصيد الحالي</th><td>{current_balance}</td></tr>
                <tr><th>عدد المستندات</th><td>{documents_count}</td></tr>
                <tr><th>إجمالي المبالغ</th><td>{total_amount}</td></tr>
            </table>

            <h3>المستندات:</h3>
            <table class="documents-table">
                <thead>
                    <tr>
                        <th>المبلغ</th>
                        <th>رقم المستند</th>
                        <th>رقم التأدية</th>
                        <th>القسم</th>
                    </tr>
                </thead>
                <tbody>
                    {documents_rows}
                </tbody>
            </table>

            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
        """

        return html_content
