#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة الحساب
"""

import os
import sys

def test_add_account():
    """اختبار إضافة حساب جديد"""
    try:
        print("🧪 اختبار إضافة حساب جديد...")
        
        # استيراد المكتبات
        from excel_manager import ExcelManager
        
        # إنشاء مدير Excel
        excel = ExcelManager()
        print("✅ تم إنشاء ExcelManager")
        
        # اختبار إضافة حساب
        account_num = "TEST001"
        account_name = "حساب اختبار"
        initial_balance = 5000
        
        print(f"📝 إضافة حساب: {account_num} - {account_name}")
        print(f"💰 الرصيد الافتتاحي: {initial_balance}")
        
        result = excel.create_account_sheet(account_num, account_name, initial_balance)
        
        if result:
            print("✅ تم إضافة الحساب بنجاح!")
            
            # التحقق من وجود الحساب
            sheet_name = f"{account_num}-{account_name}"
            if sheet_name in excel.workbook.sheetnames:
                print(f"✅ تم التحقق من وجود الحساب: {sheet_name}")
                
                # التحقق من الرصيد الافتتاحي
                ws = excel.workbook[sheet_name]
                balance = ws['A9'].value
                print(f"💰 الرصيد المحفوظ: {balance}")
                
                if balance == initial_balance:
                    print("✅ الرصيد الافتتاحي صحيح")
                else:
                    print(f"❌ الرصيد غير صحيح. متوقع: {initial_balance}, موجود: {balance}")
                
                return True
            else:
                print("❌ الحساب غير موجود في الملف")
                return False
        else:
            print("❌ فشل في إضافة الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_duplicate_account():
    """اختبار إضافة حساب مكرر"""
    try:
        print("\n🧪 اختبار إضافة حساب مكرر...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إضافة حساب
        account_num = "DUP001"
        account_name = "حساب مكرر"
        
        print(f"📝 إضافة الحساب الأول: {account_num} - {account_name}")
        result1 = excel.create_account_sheet(account_num, account_name, 1000)
        
        if result1:
            print("✅ تم إضافة الحساب الأول")
            
            # محاولة إضافة نفس الحساب مرة أخرى
            print(f"📝 محاولة إضافة الحساب مرة أخرى...")
            result2 = excel.create_account_sheet(account_num, account_name, 2000)
            
            if not result2:
                print("✅ تم رفض الحساب المكرر بنجاح")
                return True
            else:
                print("❌ تم قبول الحساب المكرر (خطأ)")
                return False
        else:
            print("❌ فشل في إضافة الحساب الأول")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الحساب المكرر: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار إضافة الحسابات")
    print("=" * 50)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 2
    
    # اختبار إضافة حساب عادي
    if test_add_account():
        success_count += 1
    
    # اختبار إضافة حساب مكرر
    if test_duplicate_account():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع الاختبارات!")
        return True
    else:
        print("❌ فشل في بعض الاختبارات")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
