#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التنسيق الأصلي الصحيح لصفحة الحساب
"""

import os
import sys

def main():
    """اختبار التنسيق الأصلي"""
    try:
        print("🔄 اختبار التنسيق الأصلي الصحيح لصفحة الحساب")
        print("=" * 60)
        
        # حذف ملف الاختبار إن وجد
        test_file = "accounting_system.xlsx"
        if os.path.exists(test_file):
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        print("\n📝 إنشاء حساب تجريبي...")
        result = excel.create_account_sheet("ORIG", "حساب التنسيق الأصلي", 1000.0)
        
        if result:
            print("✅ تم إنشاء الحساب بنجاح!")
            
            # فحص الهيكل الأساسي
            sheet_name = "ORIG-حساب التنسيق الأصلي"
            ws = excel.workbook[sheet_name]
            
            print("\n📋 فحص الهيكل الأصلي:")
            
            # مساحة الشعار (لا تزال موجودة)
            logo_content = ws['A1'].value
            print(f"   مساحة الشعار (A1): {logo_content}")
            
            # الترويسة
            header1 = ws['E1'].value
            header2 = ws['E2'].value
            print(f"   الترويسة 1: {header1}")
            print(f"   الترويسة 2: {header2}")
            
            # عنوان الأقسام في الصف 6 (التنسيق الأصلي)
            section_title = ws['A6'].value
            print(f"   عنوان الأقسام (A6): {section_title}")
            
            # عناوين الأعمدة في الصف 8 (التنسيق الأصلي)
            col1 = ws['A8'].value
            col2 = ws['B8'].value
            col3 = ws['C8'].value
            print(f"   عناوين الأعمدة (صف 8): {col1} | {col2} | {col3}")
            
            # الرصيد الافتتاحي في الصف 9 (التنسيق الأصلي)
            balance = ws['A9'].value
            balance_desc = ws['B9'].value
            print(f"   الرصيد الافتتاحي (صف 9): {balance} | {balance_desc}")
            
            # صيغة المجموع في الصف 32 (التنسيق الأصلي)
            total_formula = ws['A32'].value
            total_desc = ws['B32'].value
            print(f"   المجموع (صف 32): {total_formula} | {total_desc}")
            
            # إضافة مستندات للاختبار
            print("\n📝 إضافة مستندات...")
            documents = [
                (250.0, "DOC001", "PAY001"),
                (175.5, "DOC002", "PAY002"),
                (89.25, "DOC003", "PAY003")
            ]
            
            added_docs = 0
            for amount, doc_num, pay_num in documents:
                doc_result = excel.add_document(sheet_name, amount, doc_num, pay_num)
                if doc_result:
                    added_docs += 1
                    print(f"   ✅ تم إضافة: {amount} | {doc_num} | {pay_num}")
                else:
                    print(f"   ❌ فشل في إضافة: {amount} | {doc_num} | {pay_num}")
            
            print(f"   تم إضافة {added_docs}/{len(documents)} مستند")
            
            # فحص المستندات في الصفوف الصحيحة
            print("\n📄 فحص المستندات المضافة:")
            found_docs = 0
            for row in range(10, 32):  # الصفوف 10-31 للمستندات
                amount = ws.cell(row=row, column=1).value
                doc_num = ws.cell(row=row, column=2).value
                pay_num = ws.cell(row=row, column=3).value
                
                if amount and doc_num and pay_num:
                    found_docs += 1
                    print(f"   الصف {row}: {amount} | {doc_num} | {pay_num}")
            
            print(f"   تم العثور على {found_docs} مستند في الورقة")
            
            # فحص ترحيل الرصيد في القسم الثاني
            carry_formula = ws['D9'].value
            carry_text = ws['E9'].value
            print(f"\n🔄 ترحيل الرصيد:")
            print(f"   صيغة الترحيل (D9): {carry_formula}")
            print(f"   نص الترحيل (E9): {carry_text}")
            
            # فحص صيغة المجموع في القسم الثاني
            second_total = ws['D32'].value
            print(f"   مجموع القسم الثاني (D32): {second_total}")
            
            # تقييم التنسيق الأصلي
            checks = [
                (logo_content == "مساحة الشعار", "مساحة الشعار موجودة"),
                (section_title == "سجل المستندات والحوالات المالية", "عنوان الأقسام في الصف 6"),
                (col1 == "المبلغ", "عناوين الأعمدة في الصف 8"),
                (balance == 1000.0, "الرصيد الافتتاحي في الصف 9"),
                (isinstance(total_formula, str) and "SUM" in total_formula, "صيغة المجموع في الصف 32"),
                (added_docs >= 2, "إضافة المستندات تعمل"),
                (found_docs >= 2, "المستندات في الصفوف 10-31"),
                (carry_formula == "=A32", "ترحيل الرصيد صحيح"),
                (isinstance(second_total, str) and "SUM" in second_total, "صيغة المجموع الثاني")
            ]
            
            passed_checks = 0
            print("\n📊 تقييم التنسيق الأصلي:")
            for check_result, check_name in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                    passed_checks += 1
                else:
                    print(f"   ❌ {check_name}")
            
            print(f"\n📈 النتيجة: {passed_checks}/{len(checks)} ({passed_checks/len(checks)*100:.1f}%)")
            
            if passed_checks >= len(checks) * 0.8:  # 80% نجاح
                print("\n🎉 تم استعادة التنسيق الأصلي بنجاح!")
                print("\n✅ الهيكل الأصلي الصحيح:")
                print("   - مساحة الشعار: الصفوف 1-4")
                print("   - الترويسة: E1-E4")
                print("   - عنوان الأقسام: الصف 6")
                print("   - عناوين الأعمدة: الصف 8")
                print("   - الرصيد الافتتاحي: الصف 9")
                print("   - المستندات: الصفوف 10-31")
                print("   - المجموع: الصف 32")
                print("   - ترحيل الرصيد بين الأقسام")
                
                print("\n🔧 للاستخدام:")
                print("   1. شغل النظام: run_simple.bat")
                print("   2. أضف حساب جديد - سيكون بالتنسيق الأصلي")
                print("   3. أضف مستندات - ستظهر في الصفوف الصحيحة")
                print("   4. استخدم زر 'اختيار الشعار' لإضافة شعار")
                
                return True
            else:
                print("\n⚠️ التنسيق لا يزال يحتاج إصلاح")
                return False
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 النتيجة النهائية: تم استعادة التنسيق الأصلي الصحيح! ✨")
        print("\nالآن صفحة الحساب تعمل بالشكل الصحيح مع:")
        print("- الهيكل الأصلي المألوف")
        print("- مساحة للشعار (اختيارية)")
        print("- إضافة المستندات في المكان الصحيح")
        print("- ترحيل الرصيد بين الأقسام")
    else:
        print("\n⚠️ النتيجة النهائية: لا يزال هناك مشاكل في التنسيق")
    
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
