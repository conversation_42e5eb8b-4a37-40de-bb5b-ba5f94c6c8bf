#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص مشاكل النظام المحاسبي
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox

def check_files():
    """فحص وجود الملفات المطلوبة"""
    print("🔍 فحص الملفات المطلوبة...")
    
    required_files = [
        "app.py",
        "excel_manager.py", 
        "document_window.py",
        "search_window.py",
        "manage_accounts.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    return len(missing_files) == 0, missing_files

def check_imports():
    """فحص استيراد المكتبات"""
    print("\n📦 فحص المكتبات...")
    
    libraries = [
        ("tkinter", "واجهة المستخدم"),
        ("openpyxl", "إدارة ملفات Excel"),
        ("datetime", "التاريخ والوقت")
    ]
    
    failed_imports = []
    
    for lib, desc in libraries:
        try:
            __import__(lib)
            print(f"✅ {lib} - {desc}")
        except ImportError:
            print(f"❌ {lib} - {desc} - غير متوفر")
            failed_imports.append((lib, desc))
    
    return len(failed_imports) == 0, failed_imports

def check_excel_manager():
    """فحص ExcelManager"""
    print("\n🔧 فحص ExcelManager...")
    
    try:
        from excel_manager import ExcelManager
        print("✅ تم استيراد ExcelManager")
        
        # إنشاء مثيل
        excel = ExcelManager()
        print("✅ تم إنشاء مثيل ExcelManager")
        
        # فحص الملف
        if excel.workbook:
            print("✅ تم إنشاء/تحميل ملف Excel")
            print(f"📊 عدد الأوراق: {len(excel.workbook.sheetnames)}")
            print(f"📋 أسماء الأوراق: {excel.workbook.sheetnames}")
        else:
            print("❌ فشل في إنشاء/تحميل ملف Excel")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ExcelManager: {str(e)}")
        return False

def test_account_creation():
    """اختبار إنشاء حساب"""
    print("\n🧪 اختبار إنشاء حساب...")
    
    try:
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # اختبار إنشاء حساب
        test_account = "TEST999"
        test_name = "حساب تشخيص"
        test_balance = 1000
        
        print(f"📝 محاولة إنشاء حساب: {test_account} - {test_name}")
        
        result = excel.create_account_sheet(test_account, test_name, test_balance)
        
        if result:
            print("✅ تم إنشاء الحساب بنجاح")
            
            # التحقق من وجود الحساب
            sheet_name = f"{test_account}-{test_name}"
            if sheet_name in excel.workbook.sheetnames:
                print("✅ تم التحقق من وجود الحساب في الملف")
                
                # حذف الحساب التجريبي
                try:
                    excel.workbook.remove(excel.workbook[sheet_name])
                    excel.save_workbook()
                    print("🗑️ تم حذف الحساب التجريبي")
                except:
                    pass
                
                return True
            else:
                print("❌ الحساب غير موجود في الملف")
                return False
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الحساب: {str(e)}")
        return False

def check_gui():
    """فحص الواجهة الرسومية"""
    print("\n🖥️ فحص الواجهة الرسومية...")
    
    try:
        import tkinter as tk
        from app import AccountingApp
        
        print("✅ تم استيراد مكونات الواجهة")
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        print("✅ تم إنشاء نافذة Tkinter")
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        print("✅ تم إنشاء التطبيق")
        
        # إغلاق النافذة
        root.destroy()
        print("✅ تم إغلاق النافذة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواجهة الرسومية: {str(e)}")
        return False

def show_results(results):
    """عرض نتائج التشخيص"""
    print("\n" + "=" * 60)
    print("📋 ملخص نتائج التشخيص")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
    
    print(f"\n📊 النتيجة الإجمالية: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
        return False

def show_solutions():
    """عرض حلول للمشاكل الشائعة"""
    print("\n🔧 حلول للمشاكل الشائعة:")
    print("-" * 40)
    
    solutions = [
        "1. إذا كانت المكتبات مفقودة:",
        "   pip install openpyxl ttkthemes",
        "",
        "2. إذا كان Python غير معروف:",
        "   - تأكد من تثبيت Python",
        "   - أضف Python إلى متغير PATH",
        "",
        "3. إذا كانت الملفات مفقودة:",
        "   - تأكد من وجود جميع ملفات النظام",
        "   - أعد تحميل النظام إذا لزم الأمر",
        "",
        "4. إذا فشل إنشاء الحساب:",
        "   - تأكد من صحة البيانات المدخلة",
        "   - تأكد من عدم وجود حساب بنفس الاسم",
        "   - تأكد من صلاحيات الكتابة في المجلد",
        "",
        "5. للحصول على مساعدة إضافية:",
        "   - راجع ملف 'اقرأني.txt'",
        "   - تواصل مع الدعم الفني"
    ]
    
    for solution in solutions:
        print(solution)

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔍 أداة تشخيص النظام المحاسبي")
    print("=" * 60)
    
    # تشغيل الاختبارات
    results = {}
    
    # فحص الملفات
    files_ok, missing = check_files()
    results["فحص الملفات"] = files_ok
    
    # فحص المكتبات
    imports_ok, failed = check_imports()
    results["فحص المكتبات"] = imports_ok
    
    # فحص ExcelManager
    excel_ok = check_excel_manager()
    results["فحص ExcelManager"] = excel_ok
    
    # اختبار إنشاء حساب
    account_ok = test_account_creation()
    results["اختبار إنشاء الحساب"] = account_ok
    
    # فحص الواجهة الرسومية
    gui_ok = check_gui()
    results["فحص الواجهة الرسومية"] = gui_ok
    
    # عرض النتائج
    all_ok = show_results(results)
    
    if not all_ok:
        show_solutions()
    
    return all_ok

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ التشخيص مكتمل' if success else '❌ توجد مشاكل تحتاج إلى حل'}")
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
