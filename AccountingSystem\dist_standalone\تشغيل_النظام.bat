@echo off
chcp 65001 >nul
title نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية

cls
color 0A
echo.
echo ========================================
echo    نظام إدارة المستندات المحاسبية
echo        وزارة الصحة الأردنية
echo ========================================
echo.

echo 🔍 فحص متطلبات النظام...
echo.

REM التحقق من وجود Python
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Try the specific Python path first
if exist %PYTHON_PATH% (
    echo ✅ Python found at specific location
    set PYTHON_CMD=%PYTHON_PATH%
    goto :python_found
)

REM Try standard python command
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python found in PATH
    set PYTHON_CMD=python
    goto :python_found
)

REM Try py command
py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python found via py command
    set PYTHON_CMD=py
    goto :python_found
)

echo ❌ خطأ: Python غير مثبت أو غير موجود
echo.
echo يرجى تثبيت Python 3.7 أو أحدث من:
echo https://www.python.org/downloads/
echo.
echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
echo.
echo بعد تثبيت Python، أعد تشغيل هذا الملف
echo.
pause
exit /b 1

:python_found

echo ✅ Python متوفر
python --version

echo.
echo 📦 فحص المكتبات المطلوبة...

REM التحقق من openpyxl
python -c "import openpyxl; print('✅ openpyxl متوفر')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ openpyxl غير مثبت
    echo 📥 جاري تثبيت openpyxl...
    python -m pip install openpyxl
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت openpyxl
        echo تأكد من اتصالك بالإنترنت وأعد المحاولة
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت openpyxl بنجاح
)

REM التحقق من ttkthemes (اختياري)
python -c "import ttkthemes" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ ttkthemes غير مثبت (اختياري)
    echo 📥 جاري تثبيت ttkthemes...
    python -m pip install ttkthemes >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ تم تثبيت ttkthemes
    ) else (
        echo ⚠️ لم يتم تثبيت ttkthemes (سيعمل النظام بدونه)
    )
)

echo.
echo ✅ جميع المتطلبات متوفرة
echo.
echo 🚀 جاري تشغيل النظام...
echo.

REM تشغيل النظام
python launcher.py

REM التحقق من نتيجة التشغيل
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل النظام
    echo.
    echo نصائح لحل المشكلة:
    echo 1. تأكد من وجود جميع الملفات في نفس المجلد
    echo 2. تأكد من صحة تثبيت Python
    echo 3. تأكد من تثبيت المكتبات المطلوبة
    echo 4. تواصل مع الدعم الفني
    echo.
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
