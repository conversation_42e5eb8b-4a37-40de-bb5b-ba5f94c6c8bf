import tkinter as tk
from tkinter import ttk, messagebox

class AddDocumentWindow(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("إضافة مستند جديد")
        self.parent = parent
        
        # تكوين النافذة
        self.geometry("500x400")
        self.configure(bg='#f0f0f0')
        
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # اختيار الحساب
        ttk.Label(main_frame, text="الحساب:").grid(row=0, column=0, padx=5, pady=5)
        self.account_var = tk.StringVar()
        self.account_combo = ttk.Combobox(main_frame, textvariable=self.account_var, state='readonly')
        self.account_combo.grid(row=0, column=1, padx=5, pady=5)
        self.load_accounts()
        
        # قيمة المستند
        ttk.Label(main_frame, text="قيمة المستند:").grid(row=1, column=0, padx=5, pady=5)
        self.amount_var = tk.StringVar()
        self.amount_entry = ttk.Entry(main_frame, textvariable=self.amount_var)
        self.amount_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # رقم المستند
        ttk.Label(main_frame, text="رقم المستند:").grid(row=2, column=0, padx=5, pady=5)
        self.doc_num_var = tk.StringVar()
        self.doc_num_entry = ttk.Entry(main_frame, textvariable=self.doc_num_var)
        self.doc_num_entry.grid(row=2, column=1, padx=5, pady=5)
        
        # رقم التأدية
        ttk.Label(main_frame, text="رقم التأدية:").grid(row=3, column=0, padx=5, pady=5)
        self.pay_num_var = tk.StringVar()
        self.pay_num_entry = ttk.Entry(main_frame, textvariable=self.pay_num_var)
        self.pay_num_entry.grid(row=3, column=1, padx=5, pady=5)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        ttk.Button(buttons_frame, text="إضافة المستند",
                  command=self.add_document).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إغلاق",
                  command=self.destroy).pack(side=tk.LEFT, padx=5)
        
        # ربط زر الإدخال مع مربعات الإدخال
        self.bind("<Return>", self.bind_enter_key)
    
    def load_accounts(self):
        """تحميل قائمة الحسابات"""
        accounts = [sheet for sheet in self.parent.excel.workbook.sheetnames
                   if sheet not in ['التقارير', 'تقرير المستندات']]
        self.account_combo['values'] = accounts
        if accounts:
            self.account_combo.current(0)
    
    def add_document(self):
        """إضافة المستند"""
        try:
            # التحقق من المدخلات
            if not self.validate_inputs():
                return
            
            # إضافة المستند
            success = self.parent.excel.add_document(
                self.account_var.get(),
                float(self.amount_var.get()),
                self.doc_num_var.get(),
                self.pay_num_var.get()
            )
            
            if success:
                messagebox.showinfo("نجاح", "تم إضافة المستند بنجاح")
                self.clear_fields()
                self.destroy()  # إغلاق النافذة مباشرة بعد النجاح
            else:
                messagebox.showerror("خطأ", "لا يوجد مكان لإضافة مستند جديد")
        
        except Exception as e:
            messagebox.showerror("خطأ", str(e))
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        if not self.account_var.get():
            messagebox.showerror("خطأ", "الرجاء اختيار الحساب")
            return False
        
        try:
            amount = float(self.amount_var.get())
            if amount <= 0:
                raise ValueError
        except:
            messagebox.showerror("خطأ", "الرجاء إدخال قيمة صحيحة للمستند")
            return False
        
        if not self.doc_num_var.get():
            messagebox.showerror("خطأ", "الرجاء إدخال رقم المستند")
            return False
        
        if not self.pay_num_var.get():
            messagebox.showerror("خطأ", "الرجاء إدخال رقم التأدية")
            return False
        
        return True
    
    def clear_fields(self):
        """مسح الحقول"""
        self.amount_var.set("")
        self.doc_num_var.set("")
        self.pay_num_var.set("")
    
    def bind_enter_key(self, event=None):
        """التنقل بين الحقول باستخدام زر الإدخال"""
        current = self.focus_get()
        
        if current == self.account_combo:
            self.amount_entry.focus()
        elif current == self.amount_entry:
            self.doc_num_entry.focus()
        elif current == self.doc_num_entry:
            self.pay_num_entry.focus()
        elif current == self.pay_num_entry:
            self.add_document()  # تنفيذ عملية الإضافة عند الضغط على Enter في آخر حقل
