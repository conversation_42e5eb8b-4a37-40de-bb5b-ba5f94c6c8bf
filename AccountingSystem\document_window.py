import tkinter as tk
from tkinter import ttk, messagebox

class AddDocumentWindow(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("إضافة مستند جديد")
        self.parent = parent
        
        # تكوين النافذة
        self.geometry("500x450")  # زيادة الارتفاع لاستيعاب الخيارات الجديدة
        self.configure(bg='#f0f0f0')

        # تحميل تفضيلات المستخدم
        self.load_preferences()
        
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # اختيار الحساب
        ttk.Label(main_frame, text="الحساب:").grid(row=0, column=0, padx=5, pady=5)
        self.account_var = tk.StringVar()
        self.account_combo = ttk.Combobox(main_frame, textvariable=self.account_var, state='readonly')
        self.account_combo.grid(row=0, column=1, padx=5, pady=5)
        self.load_accounts()
        
        # قيمة المستند
        ttk.Label(main_frame, text="قيمة المستند:").grid(row=1, column=0, padx=5, pady=5)
        self.amount_var = tk.StringVar()
        self.amount_entry = ttk.Entry(main_frame, textvariable=self.amount_var)
        self.amount_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # رقم المستند
        ttk.Label(main_frame, text="رقم المستند:").grid(row=2, column=0, padx=5, pady=5)
        self.doc_num_var = tk.StringVar()
        self.doc_num_entry = ttk.Entry(main_frame, textvariable=self.doc_num_var)
        self.doc_num_entry.grid(row=2, column=1, padx=5, pady=5)
        
        # رقم التأدية
        ttk.Label(main_frame, text="رقم التأدية:").grid(row=3, column=0, padx=5, pady=5)
        self.pay_num_var = tk.StringVar()
        self.pay_num_entry = ttk.Entry(main_frame, textvariable=self.pay_num_var)
        self.pay_num_entry.grid(row=3, column=1, padx=5, pady=5)
        
        # خيارات الإغلاق
        close_frame = ttk.LabelFrame(main_frame, text="خيارات الإغلاق", padding="5")
        close_frame.grid(row=4, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))

        self.close_option = tk.StringVar(value=getattr(self, 'close_option_pref', 'auto'))
        ttk.Radiobutton(close_frame, text="إغلاق تلقائي بعد الإضافة",
                       variable=self.close_option, value="auto").pack(anchor=tk.W)
        ttk.Radiobutton(close_frame, text="البقاء مفتوحاً لإضافة مستندات أخرى",
                       variable=self.close_option, value="stay").pack(anchor=tk.W)

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=5, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="إضافة المستند",
                  command=self.add_document).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إضافة وإغلاق",
                  command=self.add_and_close).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إغلاق",
                  command=self.destroy).pack(side=tk.LEFT, padx=5)
        
        # ربط اختصارات لوحة المفاتيح
        self.bind("<Return>", self.bind_enter_key)
        self.bind("<Control-Return>", lambda e: self.add_and_close())  # Ctrl+Enter للإضافة والإغلاق
        self.bind("<Escape>", lambda e: self.destroy())  # Escape للإغلاق
        self.bind("<F1>", lambda e: self.show_help())  # F1 للمساعدة
    
    def load_accounts(self):
        """تحميل قائمة الحسابات"""
        accounts = [sheet for sheet in self.parent.excel.workbook.sheetnames
                   if sheet not in ['التقارير', 'تقرير المستندات']]
        self.account_combo['values'] = accounts
        if accounts:
            self.account_combo.current(0)
    
    def add_document(self):
        """إضافة المستند"""
        try:
            # التحقق من المدخلات
            if not self.validate_inputs():
                return
            
            # إضافة المستند
            success = self.parent.excel.add_document(
                self.account_var.get(),
                float(self.amount_var.get()),
                self.doc_num_var.get(),
                self.pay_num_var.get()
            )
            
            if success:
                # معالجة الإغلاق حسب اختيار المستخدم
                if self.close_option.get() == "auto":
                    # إغلاق تلقائي
                    self.destroy()
                else:
                    # البقاء مفتوحاً - مسح الحقول للمستند التالي
                    self.clear_fields()
                    self.amount_entry.focus()  # التركيز على حقل المبلغ
                    # تحديث قائمة الحسابات في حالة إضافة حساب جديد
                    self.load_accounts()
            else:
                messagebox.showerror("خطأ", "لا يوجد مكان لإضافة مستند جديد")
        
        except Exception as e:
            messagebox.showerror("خطأ", str(e))

    def add_and_close(self):
        """إضافة المستند وإغلاق النافذة مباشرة"""
        try:
            # التحقق من المدخلات
            if not self.validate_inputs():
                return

            # إضافة المستند
            success = self.parent.excel.add_document(
                self.account_var.get(),
                float(self.amount_var.get()),
                self.doc_num_var.get(),
                self.pay_num_var.get()
            )

            if success:
                # إغلاق مباشر بغض النظر عن الخيار المحدد
                self.destroy()
            else:
                messagebox.showerror("خطأ", "لا يوجد مكان لإضافة مستند جديد")

        except Exception as e:
            messagebox.showerror("خطأ", str(e))

    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        if not self.account_var.get():
            messagebox.showerror("خطأ", "الرجاء اختيار الحساب")
            return False
        
        try:
            amount = float(self.amount_var.get())
            if amount <= 0:
                raise ValueError
        except:
            messagebox.showerror("خطأ", "الرجاء إدخال قيمة صحيحة للمستند")
            return False
        
        if not self.doc_num_var.get():
            messagebox.showerror("خطأ", "الرجاء إدخال رقم المستند")
            return False
        
        if not self.pay_num_var.get():
            messagebox.showerror("خطأ", "الرجاء إدخال رقم التأدية")
            return False
        
        return True
    
    def clear_fields(self):
        """مسح الحقول"""
        self.amount_var.set("")
        self.doc_num_var.set("")
        self.pay_num_var.set("")
    
    def bind_enter_key(self, event=None):
        """التنقل بين الحقول باستخدام زر الإدخال"""
        current = self.focus_get()

        if current == self.account_combo:
            self.amount_entry.focus()
        elif current == self.amount_entry:
            self.doc_num_entry.focus()
        elif current == self.doc_num_entry:
            self.pay_num_entry.focus()
        elif current == self.pay_num_entry:
            # تنفيذ عملية الإضافة حسب الخيار المحدد
            if self.close_option.get() == "auto":
                self.add_and_close()  # إضافة وإغلاق
            else:
                self.add_document()  # إضافة والبقاء مفتوحاً

    def show_help(self):
        """عرض نافذة المساعدة"""
        help_text = """
اختصارات لوحة المفاتيح:

• Enter: التنقل بين الحقول أو إضافة المستند
• Ctrl+Enter: إضافة المستند وإغلاق النافذة
• Escape: إغلاق النافذة
• F1: عرض هذه المساعدة

خيارات الإغلاق:
• إغلاق تلقائي: النافذة تُغلق بعد إضافة كل مستند
• البقاء مفتوحاً: النافذة تبقى مفتوحة لإضافة مستندات متعددة

الأزرار:
• إضافة المستند: يضيف المستند حسب الخيار المحدد
• إضافة وإغلاق: يضيف المستند ويغلق النافذة مباشرة
• إغلاق: يغلق النافذة بدون إضافة
        """
        messagebox.showinfo("المساعدة - إضافة المستندات", help_text)

    def load_preferences(self):
        """تحميل تفضيلات المستخدم"""
        try:
            import os
            prefs_file = "document_window_prefs.txt"
            if os.path.exists(prefs_file):
                with open(prefs_file, 'r', encoding='utf-8') as f:
                    close_option = f.read().strip()
                    if close_option in ['auto', 'stay']:
                        self.close_option_pref = close_option
                    else:
                        self.close_option_pref = 'auto'
            else:
                self.close_option_pref = 'auto'
        except:
            self.close_option_pref = 'auto'

    def save_preferences(self):
        """حفظ تفضيلات المستخدم"""
        try:
            with open("document_window_prefs.txt", 'w', encoding='utf-8') as f:
                f.write(self.close_option.get())
        except:
            pass  # تجاهل أخطاء الحفظ

    def destroy(self):
        """إغلاق النافذة مع حفظ التفضيلات"""
        self.save_preferences()
        super().destroy()
