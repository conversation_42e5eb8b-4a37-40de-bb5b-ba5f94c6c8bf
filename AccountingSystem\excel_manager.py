import openpyxl
from openpyxl.styles import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PatternFill, Border, Side
from datetime import datetime
import os
from tkinter import messagebox

class ExcelManager:
    def __init__(self):
        self.current_file = "accounting_system.xlsx"
        self.file_path = self.current_file  # إضافة خاصية file_path
        self.workbook = None
        self.load_or_create_workbook()
    
    def load_or_create_workbook(self):
        """تحميل الملف الموجود أو إنشاء ملف جديد"""
        if os.path.exists(self.current_file):
            try:
                self.workbook = openpyxl.load_workbook(self.current_file)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح الملف: {str(e)}")
                self.create_new_workbook()
        else:
            self.create_new_workbook()
    
    def create_new_workbook(self):
        """إنشاء ملف Excel جديد"""
        self.workbook = openpyxl.Workbook()
        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])
        self.save_workbook()
    
    def save_workbook(self, file_path=None):
        """حفظ الملف"""
        try:
            save_path = file_path or self.current_file
            self.workbook.save(save_path)
            if file_path:
                self.current_file = file_path
                self.file_path = file_path
            return True
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")
            return False

    def save(self, file_path=None):
        """دالة حفظ بديلة للتوافق"""
        return self.save_workbook(file_path)
    def create_account_sheet(self, account_num, account_name, initial_balance=0):
        """إنشاء صفحة حساب جديدة"""
        try:
            # التحقق من صحة البيانات
            if not account_num or not account_name:
                messagebox.showerror("خطأ", "رقم الحساب واسم الحساب مطلوبان")
                return False

            # تنظيف البيانات
            account_num = str(account_num).strip()
            account_name = str(account_name).strip()

            # التحقق من الرصيد الافتتاحي
            try:
                initial_balance = float(initial_balance)
            except (ValueError, TypeError):
                initial_balance = 0

            sheet_name = f"{account_num}-{account_name}"

            # التحقق من عدم وجود الحساب
            if sheet_name in self.workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{sheet_name}' موجود مسبقاً")
                return False

            # التحقق من صحة اسم الورقة
            if len(sheet_name) > 31:  # حد Excel لأسماء الأوراق
                messagebox.showerror("خطأ", "اسم الحساب طويل جداً (الحد الأقصى 31 حرف)")
                return False

            print(f"إنشاء حساب جديد: {sheet_name}")  # للتشخيص

            # إنشاء الورقة
            ws = self.workbook.create_sheet(sheet_name)

            # تعيين اتجاه الصفحة من اليمين إلى اليسار
            ws.sheet_properties.rightToLeft = True
            ws.sheet_view.rightToLeft = True  # إضافة إعداد إضافي للتأكد

            print(f"✅ تم تعيين اتجاه الصفحة من اليمين لليسار")  # للتشخيص

            # إعداد الترويسة
            self._setup_header(ws, account_num, account_name)

            # إعداد الأقسام
            self._setup_sections(ws, initial_balance)

            print(f"تم إنشاء الحساب: {sheet_name}")  # للتشخيص

            # إنشاء/تحديث التقرير الإجمالي تلقائياً
            print(f"📊 إنشاء/تحديث التقرير الإجمالي...")
            self.create_summary_report()

            # حفظ التغييرات
            save_result = self.save_workbook()
            if save_result:
                print(f"تم حفظ الحساب: {sheet_name}")  # للتشخيص
            else:
                print(f"فشل في حفظ الحساب: {sheet_name}")  # للتشخيص

            return save_result

        except Exception as e:
            error_msg = f"خطأ في إنشاء الحساب: {str(e)}"
            print(error_msg)  # للتشخيص
            messagebox.showerror("خطأ", error_msg)
            return False

    def _setup_header(self, ws, account_num, account_name):
        """إعداد ترويسة الصفحة مع مساحة للشعار وتنسيق محسن"""
        try:
            from openpyxl.styles import PatternFill, Border, Side

            # مساحة للشعار (4 صفوف)
            # دمج خلايا للشعار في الزاوية اليسرى
            ws.merge_cells('A1:D4')
            logo_cell = ws['A1']
            logo_cell.value = "مساحة الشعار"
            logo_cell.font = Font(size=10, bold=True, color="808080")
            logo_cell.alignment = Alignment(horizontal='center', vertical='center')
            logo_cell.fill = PatternFill(start_color="F0F0F0", end_color="F0F0F0", fill_type="solid")
            logo_cell.border = Border(all=Side(style='thin'))

            # تعيين ارتفاع الصفوف للشعار
            for row in range(1, 5):
                ws.row_dimensions[row].height = 25

            # الترويسة الرسمية (بجانب الشعار)
            ws.merge_cells('E1:R1')
            header1 = ws['E1']
            header1.value = "المملكة الأردنية الهاشمية"
            header1.font = Font(size=16, bold=True, color="1F4E79")
            header1.alignment = Alignment(horizontal='center', vertical='center')
            header1.fill = PatternFill(start_color="D9E2F3", end_color="D9E2F3", fill_type="solid")
            header1.border = Border(all=Side(style='thin'))

            ws.merge_cells('E2:R2')
            header2 = ws['E2']
            header2.value = "وزارة الصحة"
            header2.font = Font(size=14, bold=True, color="1F4E79")
            header2.alignment = Alignment(horizontal='center', vertical='center')
            header2.fill = PatternFill(start_color="D9E2F3", end_color="D9E2F3", fill_type="solid")
            header2.border = Border(all=Side(style='thin'))

            ws.merge_cells('E3:R3')
            header3 = ws['E3']
            header3.value = f"حساب: {account_name} - {account_num}"
            header3.font = Font(size=13, bold=True, color="C5504B")
            header3.alignment = Alignment(horizontal='center', vertical='center')
            header3.fill = PatternFill(start_color="FCE4D6", end_color="FCE4D6", fill_type="solid")
            header3.border = Border(all=Side(style='thin'))

            ws.merge_cells('E4:R4')
            header4 = ws['E4']
            current_date = datetime.now().strftime("%Y/%m/%d")
            header4.value = f"تاريخ الإنشاء: {current_date}"
            header4.font = Font(size=11, bold=True, color="70AD47")
            header4.alignment = Alignment(horizontal='center', vertical='center')
            header4.fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
            header4.border = Border(all=Side(style='thin'))

        except Exception as e:
            print(f"خطأ في إعداد الترويسة: {str(e)}")
            raise
    def _setup_sections(self, ws, initial_balance):
        """إعداد أقسام الصفحة بالتنسيق الأصلي الصحيح"""
        try:
            print(f"إعداد الأقسام مع رصيد افتتاحي: {initial_balance}")

            # تعريف الحدود
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # إعداد عنوان الأقسام في الصف 6 (بدل<|im_start|> من 5)
            ws.merge_cells('A6:R6')
            title_cell = ws['A6']
            title_cell.value = "سجل المستندات والحوالات المالية"
            title_cell.font = Font(bold=True, size=14)
            title_cell.alignment = Alignment(horizontal='center')
            title_cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            for i in range(6):
                col = chr(65 + (i * 3))  # A, D, G, J, M, P
                print(f"إعداد القسم {i+1} في العمود {col}")

                # العناوين في الصف 8 (التنسيق الأصلي)
                ws[f'{col}8'] = "المبلغ"
                ws[f'{chr(ord(col) + 1)}8'] = "مستند الصرف"
                ws[f'{chr(ord(col) + 2)}8'] = "رقم التأدية"

                # تنسيق العناوين (التنسيق الأصلي البسيط)
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}8']
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    cell.alignment = Alignment(horizontal='center')
                    cell.border = thin_border

                    # إضافة حدود لجميع الخلايا في العمود (حتى الصف 32)
                    for row in range(9, 33):  # من الصف 9 إلى 32
                        cell = ws[f'{col_letter}{row}']
                        cell.border = thin_border
                        cell.alignment = Alignment(horizontal='center')

                # القيم الأولية - الرصيد الافتتاحي في الصف 9
                if i == 0:
                    ws[f'{col}9'] = initial_balance
                    ws[f'{chr(ord(col) + 1)}9'] = "رصيد افتتاحي"
                    print(f"تم تعيين الرصيد الافتتاحي: {initial_balance}")
                else:
                    # ترحيل الرصيد من القسم السابق
                    prev_col = chr(ord(col) - 3)
                    ws[f'{col}9'] = f"={prev_col}32"  # يشير إلى المجموع في الصف 32
                    ws[f'{chr(ord(col) + 1)}9'] = "ما قبله"
                    print(f"تم تعيين صيغة ترحيل الرصيد: {col}9 = {prev_col}32")

                # صيغة المجموع في الصف 32
                ws[f'{col}32'] = f"=SUM({col}9:{col}31)"
                ws[f'{chr(ord(col) + 1)}32'] = "الإجمالي"

                # تنسيق خلايا المجموع
                for j in range(3):
                    col_letter = chr(ord(col) + j)
                    cell = ws[f'{col_letter}32']
                    cell.border = Border(
                        top=Side(style='double'),
                        bottom=Side(style='double'),
                        left=Side(style='thin'),
                        right=Side(style='thin')
                    )
                    cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
                    cell.alignment = Alignment(horizontal='center')

            # إعداد عرض الأعمدة
            column_widths = {
                'A': 15, 'B': 18, 'C': 15,  # القسم الأول
                'D': 15, 'E': 18, 'F': 15,  # القسم الثاني
                'G': 15, 'H': 18, 'I': 15,  # القسم الثالث
                'J': 15, 'K': 18, 'L': 15,  # القسم الرابع
                'M': 15, 'N': 18, 'O': 15,  # القسم الخامس
                'P': 15, 'Q': 18, 'R': 15   # القسم السادس
            }

            for col_letter, width in column_widths.items():
                ws.column_dimensions[col_letter].width = width

            print("تم إنجاز إعداد جميع الأقسام بالتنسيق الأصلي")

        except Exception as e:
            print(f"خطأ في إعداد الأقسام: {str(e)}")
            raise
                
    def add_document(self, sheet_name, amount, doc_num, pay_num):
        """إضافة مستند إلى حساب"""
        try:
            print(f"📝 محاولة إضافة مستند إلى الحساب: {sheet_name}")  # للتشخيص
            print(f"   المبلغ: {amount}, رقم المستند: {doc_num}, رقم التأدية: {pay_num}")  # للتشخيص

            # التحقق من وجود الحساب
            if sheet_name not in self.workbook.sheetnames:
                error_msg = f"الحساب '{sheet_name}' غير موجود"
                print(f"❌ {error_msg}")  # للتشخيص
                messagebox.showerror("خطأ", error_msg)
                return False

            ws = self.workbook[sheet_name]
            print(f"✅ تم العثور على ورقة الحساب")  # للتشخيص

            # تشخيص الحساب أولاً
            print(f"🔍 تشخيص الحساب قبل إضافة المستند...")
            self.diagnose_account(sheet_name)

            # البحث عن خلية فارغة
            empty_cell = self._find_empty_cell(ws)

            if not empty_cell:
                error_msg = "لا يوجد مكان لإضافة مستند جديد (جميع الأقسام ممتلئة)"
                print(f"❌ {error_msg}")  # للتشخيص

                # تشخيص إضافي
                print(f"🔍 تشخيص إضافي للمشكلة...")
                self.diagnose_account(sheet_name)

                messagebox.showerror("خطأ", error_msg)
                return False

            row, col = empty_cell
            print(f"✅ تم العثور على خلية فارغة في الصف {row}, العمود {col}")  # للتشخيص

            # التأكد من أن القيمة رقمية
            try:
                amount_float = float(amount)
                print(f"✅ المبلغ صحيح: {amount_float}")  # للتشخيص
            except ValueError:
                error_msg = "يرجى إدخال قيمة رقمية صحيحة للمبلغ"
                print(f"❌ {error_msg}")  # للتشخيص
                messagebox.showerror("خطأ", error_msg)
                return False

            # إضافة المستند
            print(f"📝 إضافة البيانات إلى الخلايا...")  # للتشخيص

            # المبلغ
            amount_cell = ws.cell(row=row, column=col)
            amount_cell.value = amount_float
            amount_cell.number_format = '#,##0.000'  # تنسيق المبلغ بثلاث خانات عشرية
            print(f"   المبلغ في {chr(64+col)}{row}: {amount_float}")  # للتشخيص

            # رقم المستند
            doc_cell = ws.cell(row=row, column=col+1)
            doc_cell.value = str(doc_num)
            doc_cell.alignment = Alignment(horizontal='center')
            print(f"   رقم المستند في {chr(64+col+1)}{row}: {doc_num}")  # للتشخيص

            # رقم التأدية
            pay_cell = ws.cell(row=row, column=col+2)
            pay_cell.value = str(pay_num)
            pay_cell.alignment = Alignment(horizontal='center')
            print(f"   رقم التأدية في {chr(64+col+2)}{row}: {pay_num}")  # للتشخيص

            # إضافة حدود للخلايا
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            for i in range(3):
                cell = ws.cell(row=row, column=col+i)
                cell.border = thin_border
                cell.alignment = Alignment(horizontal='center')

            print(f"✅ تم إضافة المستند بنجاح")  # للتشخيص

            # تحديث التقرير الإجمالي تلقائياً
            print(f"📊 تحديث التقرير الإجمالي بعد إضافة المستند...")
            self.create_summary_report()

            # حفظ الملف (Excel سيحسب الصيغ تلقائياً عند فتح الملف)
            save_result = self.save_workbook()
            if save_result:
                print(f"✅ تم حفظ الملف بنجاح")  # للتشخيص
            else:
                print(f"❌ فشل في حفظ الملف")  # للتشخيص

            return save_result

        except KeyError as e:
            error_msg = f"الحساب '{sheet_name}' غير موجود في الملف"
            print(f"❌ {error_msg}: {str(e)}")  # للتشخيص
            messagebox.showerror("خطأ", error_msg)
            return False
        except Exception as e:
            error_msg = f"خطأ في إضافة المستند: {str(e)}"
            print(f"❌ {error_msg}")  # للتشخيص
            messagebox.showerror("خطأ", error_msg)
            return False
            
    def _find_empty_cell(self, ws):
        """البحث عن خلية فارغة بالتنسيق الأصلي"""
        print("🔍 البحث عن خلية فارغة...")

        # فحص كل قسم من الأقسام الستة
        for i in range(6):  # الأقسام الستة
            col_start = chr(65 + (i * 3))  # A, D, G, J, M, P
            col_num = ord(col_start) - 64  # تحويل الحرف إلى رقم العمود

            print(f"فحص القسم {i+1} - العمود {col_start} (رقم {col_num})")

            # البحث عن خلية فارغة في هذا القسم (من الصف 10 إلى 31)
            for row in range(10, 32):  # من الصف 10 إلى 31 (المستندات فقط)
                try:
                    # فحص الخلية
                    cell = ws.cell(row=row, column=col_num)
                    cell_value = cell.value

                    print(f"  فحص الخلية {col_start}{row}: '{cell_value}' (نوع: {type(cell_value)})")

                    # التحقق من أن الخلية فارغة حقاً
                    is_empty = (
                        cell_value is None or
                        cell_value == "" or
                        (isinstance(cell_value, str) and cell_value.strip() == "")
                    )

                    if is_empty:
                        print(f"✅ وجدت خلية فارغة في {col_start}{row}")

                        # التحقق من أن الخلايا المجاورة فارغة أيضاً (للمستند الكامل)
                        doc_cell = ws.cell(row=row, column=col_num+1).value
                        pay_cell = ws.cell(row=row, column=col_num+2).value

                        if (doc_cell is None or doc_cell == "") and (pay_cell is None or pay_cell == ""):
                            print(f"✅ الخلايا المجاورة فارغة أيضاً - يمكن إضافة المستند")
                            return (row, col_num)
                        else:
                            print(f"⚠️ الخلايا المجاورة ليست فارغة: doc='{doc_cell}', pay='{pay_cell}'")

                except Exception as e:
                    print(f"❌ خطأ في فحص الخلية {col_start}{row}: {str(e)}")
                    continue

        print("❌ لم يتم العثور على خلية فارغة في جميع الأقسام")

        # طباعة تشخيص إضافي
        print("📊 تشخيص إضافي:")
        for i in range(6):
            col_start = chr(65 + (i * 3))
            col_num = ord(col_start) - 64
            print(f"  القسم {i+1} ({col_start}):")

            empty_count = 0
            for row in range(10, 32):  # من الصف 10 إلى 31
                cell_value = ws.cell(row=row, column=col_num).value
                if cell_value is None or cell_value == "":
                    empty_count += 1

            print(f"    خلايا فارغة: {empty_count}/22")

        return None

    def diagnose_account(self, sheet_name):
        """تشخيص حالة الحساب بالتنسيق الأصلي"""
        try:
            print(f"🔍 تشخيص الحساب: {sheet_name}")

            if sheet_name not in self.workbook.sheetnames:
                print(f"❌ الحساب غير موجود")
                return False

            ws = self.workbook[sheet_name]

            print(f"📊 تحليل الحساب:")
            print(f"  اسم الورقة: {sheet_name}")
            print(f"  أبعاد الورقة: {ws.max_row} صف × {ws.max_column} عمود")

            # فحص كل قسم
            for i in range(6):
                col_start = chr(65 + (i * 3))
                col_num = ord(col_start) - 64

                print(f"\n  القسم {i+1} (العمود {col_start}):")

                # فحص الرصيد الافتتاحي في الصف 9
                opening_value = ws.cell(row=9, column=col_num).value
                print(f"    الرصيد الافتتاحي (صف 9): {opening_value}")

                # فحص المستندات
                document_count = 0
                empty_count = 0

                for row in range(10, 32):  # من الصف 10 إلى 31
                    amount = ws.cell(row=row, column=col_num).value
                    doc_num = ws.cell(row=row, column=col_num+1).value
                    pay_num = ws.cell(row=row, column=col_num+2).value

                    if amount is not None and amount != "":
                        document_count += 1
                        print(f"      صف {row}: {amount} | {doc_num} | {pay_num}")
                    else:
                        empty_count += 1

                print(f"    المستندات الموجودة: {document_count}")
                print(f"    الخلايا الفارغة: {empty_count}")

                # فحص المجموع في الصف 32
                total_value = ws.cell(row=32, column=col_num).value
                print(f"    المجموع (صف 32): {total_value}")

            return True

        except Exception as e:
            print(f"❌ خطأ في تشخيص الحساب: {str(e)}")
            return False

    def create_report(self):
        """إنشاء تقرير المجاميع"""
        if 'التقارير' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['التقارير'])
            
        ws = self.workbook.create_sheet('التقارير')
        ws.sheet_properties.rightToLeft = True
        
        # إعداد العناوين
        headers = ['الحساب', 'الرصيد الافتتاحي', 'مجموع المستندات', 'الرصيد النهائي']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # إضافة الحسابات
        row = 2
        total_opening = 0
        total_documents = 0
        total_final = 0
        
        for sheet_name in self.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                ws_account = self.workbook[sheet_name]
                
                # الرصيد الافتتاحي (أول قيمة في القسم الأول)
                opening_balance = ws_account['A9'].value or 0
                total_opening += opening_balance

                # مجموع المستندات (مجموع جميع الأقسام)
                documents_total = 0
                for i in range(6):
                    col = chr(65 + (i * 3))  # A, D, G, J, M, P
                    section_total = ws_account[f'{col}32'].value
                    if isinstance(section_total, (int, float)):
                        documents_total += section_total
                total_documents += documents_total
                
                # الرصيد النهائي
                final_balance = opening_balance + documents_total
                total_final += final_balance
                
                # إضافة الصف
                ws.cell(row=row, column=1).value = sheet_name
                ws.cell(row=row, column=2).value = opening_balance
                ws.cell(row=row, column=3).value = documents_total
                ws.cell(row=row, column=4).value = final_balance
                
                # تنسيق الخلايا
                for col in range(1, 5):
                    ws.cell(row=row, column=col).border = Border(all=Side(style='thin'))
                
                row += 1
        
        # إضافة المجاميع
        ws.cell(row=row, column=1).value = "المجموع"
        ws.cell(row=row, column=2).value = total_opening
        ws.cell(row=row, column=3).value = total_documents
        ws.cell(row=row, column=4).value = total_final
        
        # تنسيق صف المجموع
        for col in range(1, 5):
            cell = ws.cell(row=row, column=col)
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
            cell.fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
        
        # تنسيق عرض الأعمدة
        for col in range(1, 5):
            ws.column_dimensions[chr(64 + col)].width = 20
        
        self.save_workbook()
        return True

    def create_documents_report(self, account_name=None):
        """إنشاء تقرير المستندات"""
        sheet_name = 'تقرير المستندات'
        if sheet_name in self.workbook.sheetnames:
            self.workbook.remove(self.workbook[sheet_name])

        ws = self.workbook.create_sheet(sheet_name)
        ws.sheet_properties.rightToLeft = True

        # إعداد العناوين
        headers = ['الحساب', 'المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        row = 2
        # تجميع المستندات من كل حساب
        for sheet_name in self.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                if account_name and sheet_name != account_name:
                    continue

                ws_account = self.workbook[sheet_name]
                for section in range(6):  # الأقسام الستة
                    col_start = 1 + (section * 3)
                    for doc_row in range(10, 32):  # من الصف 10 إلى 31
                        amount = ws_account.cell(row=doc_row, column=col_start).value
                        doc_num = ws_account.cell(row=doc_row, column=col_start+1).value
                        pay_num = ws_account.cell(row=doc_row, column=col_start+2).value

                        if amount and doc_num and pay_num:  # إذا كان هناك مستند
                            ws.cell(row=row, column=1).value = sheet_name
                            ws.cell(row=row, column=2).value = amount
                            ws.cell(row=row, column=3).value = doc_num
                            ws.cell(row=row, column=4).value = pay_num
                            ws.cell(row=row, column=5).value = f"القسم {section + 1}"

                            # تنسيق الخلايا
                            for col in range(1, 6):
                                ws.cell(row=row, column=col).border = Border(all=Side(style='thin'))

                            row += 1

        # تنسيق العرض
        for col in range(1, 6):
            ws.column_dimensions[chr(64 + col)].width = 15

        self.save_workbook()
        return True

    def create_summary_report(self):
        """إنشاء تقرير إجمالي محسن لجميع الحسابات"""
        try:
            print("📊 إنشاء التقرير الإجمالي...")

            # حذف التقرير القديم إن وجد
            report_sheet_name = 'التقرير الإجمالي'
            if report_sheet_name in self.workbook.sheetnames:
                self.workbook.remove(self.workbook[report_sheet_name])
                print("🗑️ تم حذف التقرير القديم")

            # إنشاء ورقة التقرير الجديدة
            ws = self.workbook.create_sheet(report_sheet_name, 0)  # في المقدمة
            ws.sheet_properties.rightToLeft = True
            print("✅ تم إنشاء ورقة التقرير الجديدة")

            # إعداد الترويسة
            self._setup_report_header(ws)

            # إعداد عناوين الأعمدة
            self._setup_report_columns(ws)

            # إضافة بيانات الحسابات
            self._add_accounts_data(ws)

            # إضافة المجاميع النهائية
            self._add_report_totals(ws)

            # تنسيق التقرير
            self._format_report(ws)

            print("✅ تم إنشاء التقرير الإجمالي بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير الإجمالي: {str(e)}")
            return False

    def _setup_report_header(self, ws):
        """إعداد ترويسة التقرير"""
        try:
            # العنوان الرئيسي
            ws.merge_cells('A1:F1')
            ws['A1'] = "المملكة الأردنية الهاشمية"
            ws['A1'].font = Font(bold=True, size=14)
            ws['A1'].alignment = Alignment(horizontal='center')

            # وزارة الصحة
            ws.merge_cells('A2:F2')
            ws['A2'] = "وزارة الصحة"
            ws['A2'].font = Font(bold=True, size=12)
            ws['A2'].alignment = Alignment(horizontal='center')

            # عنوان التقرير
            ws.merge_cells('A3:F3')
            ws['A3'] = "التقرير الإجمالي لأرصدة الحسابات"
            ws['A3'].font = Font(bold=True, size=12)
            ws['A3'].alignment = Alignment(horizontal='center')

            # تاريخ التقرير
            from datetime import datetime
            ws.merge_cells('A4:F4')
            ws['A4'] = f"تاريخ التقرير: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
            ws['A4'].font = Font(size=10)
            ws['A4'].alignment = Alignment(horizontal='center')

        except Exception as e:
            print(f"❌ خطأ في إعداد ترويسة التقرير: {str(e)}")
            raise

    def _setup_report_columns(self, ws):
        """إعداد عناوين أعمدة التقرير"""
        try:
            headers = [
                'رقم الحساب',
                'اسم الحساب',
                'الرصيد الافتتاحي',
                'مجموع المستندات',
                'الرصيد النهائي',
                'عدد المستندات'
            ]

            # إضافة العناوين في الصف 6
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=6, column=col)
                cell.value = header
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(all=Side(style='thin'))

            print("✅ تم إعداد عناوين الأعمدة")

        except Exception as e:
            print(f"❌ خطأ في إعداد عناوين الأعمدة: {str(e)}")
            raise

    def _add_accounts_data(self, ws):
        """إضافة بيانات الحسابات إلى التقرير"""
        try:
            row = 7  # بداية البيانات

            for sheet_name in self.workbook.sheetnames:
                if sheet_name in ['التقرير الإجمالي', 'التقارير', 'تقرير المستندات']:
                    continue

                print(f"📊 معالجة الحساب: {sheet_name}")

                # تحليل اسم الحساب
                if '-' in sheet_name:
                    account_num, account_name = sheet_name.split('-', 1)
                else:
                    account_num = sheet_name
                    account_name = sheet_name

                ws_account = self.workbook[sheet_name]

                # الرصيد الافتتاحي (من الخلية A8)
                opening_balance = ws_account['A8'].value or 0
                if isinstance(opening_balance, str):
                    opening_balance = 0

                # حساب مجموع المستندات وعددها
                documents_total = 0
                documents_count = 0

                # فحص جميع الأقسام الستة
                for i in range(6):
                    col_letter = chr(65 + (i * 3))  # A, D, G, J, M, P

                    # مجموع القسم من الخلية 31
                    section_total = ws_account[f'{col_letter}31'].value
                    if isinstance(section_total, (int, float)):
                        documents_total += section_total

                    # عد المستندات في هذا القسم
                    for doc_row in range(9, 31):  # صفوف المستندات من 9 إلى 30
                        amount = ws_account.cell(row=doc_row, column=ord(col_letter)-64).value
                        if amount and isinstance(amount, (int, float)) and amount != 0:
                            documents_count += 1

                # الرصيد النهائي = مجموع جميع الأقسام (يتضمن الرصيد الافتتاحي)
                final_balance = documents_total

                # إضافة البيانات إلى التقرير
                ws.cell(row=row, column=1).value = account_num
                ws.cell(row=row, column=2).value = account_name
                ws.cell(row=row, column=3).value = opening_balance
                ws.cell(row=row, column=4).value = documents_total
                ws.cell(row=row, column=5).value = final_balance
                ws.cell(row=row, column=6).value = documents_count

                # تنسيق الصف
                for col in range(1, 7):
                    cell = ws.cell(row=row, column=col)
                    cell.border = Border(all=Side(style='thin'))
                    cell.alignment = Alignment(horizontal='center')

                    # تنسيق الأرقام
                    if col in [3, 4, 5]:  # أعمدة المبالغ
                        cell.number_format = '#,##0.000'

                row += 1

            # حفظ رقم آخر صف للمجاميع
            self.last_data_row = row - 1
            print(f"✅ تم إضافة بيانات {self.last_data_row - 6} حساب")

        except Exception as e:
            print(f"❌ خطأ في إضافة بيانات الحسابات: {str(e)}")
            raise

    def _add_report_totals(self, ws):
        """إضافة المجاميع النهائية للتقرير"""
        try:
            if not hasattr(self, 'last_data_row'):
                return

            totals_row = self.last_data_row + 2

            # عنوان المجاميع
            ws.merge_cells(f'A{totals_row}:B{totals_row}')
            ws[f'A{totals_row}'] = "المجموع الكلي"
            ws[f'A{totals_row}'].font = Font(bold=True)
            ws[f'A{totals_row}'].alignment = Alignment(horizontal='center')
            ws[f'A{totals_row}'].fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")

            # حساب المجاميع
            start_row = 7
            end_row = self.last_data_row

            # مجموع الأرصدة الافتتاحية
            ws[f'C{totals_row}'] = f"=SUM(C{start_row}:C{end_row})"

            # مجموع المستندات
            ws[f'D{totals_row}'] = f"=SUM(D{start_row}:D{end_row})"

            # مجموع الأرصدة النهائية
            ws[f'E{totals_row}'] = f"=SUM(E{start_row}:E{end_row})"

            # مجموع عدد المستندات
            ws[f'F{totals_row}'] = f"=SUM(F{start_row}:F{end_row})"

            # تنسيق صف المجاميع
            for col in range(1, 7):
                cell = ws.cell(row=totals_row, column=col)
                cell.font = Font(bold=True)
                cell.border = Border(all=Side(style='thick'))
                cell.fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')

                # تنسيق الأرقام
                if col in [3, 4, 5]:
                    cell.number_format = '#,##0.000'

            print("✅ تم إضافة المجاميع النهائية")

        except Exception as e:
            print(f"❌ خطأ في إضافة المجاميع: {str(e)}")
            raise

    def _format_report(self, ws):
        """تنسيق التقرير النهائي"""
        try:
            # تعيين عرض الأعمدة
            column_widths = {
                'A': 15,  # رقم الحساب
                'B': 25,  # اسم الحساب
                'C': 18,  # الرصيد الافتتاحي
                'D': 18,  # مجموع المستندات
                'E': 18,  # الرصيد النهائي
                'F': 15   # عدد المستندات
            }

            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width

            # تجميد الصفوف العلوية
            ws.freeze_panes = 'A7'

            print("✅ تم تنسيق التقرير")

        except Exception as e:
            print(f"❌ خطأ في تنسيق التقرير: {str(e)}")
            raise
