import openpyxl
from openpyxl.styles import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PatternFill, Border, Side
from datetime import datetime
import os
from tkinter import messagebox

class ExcelManager:
    def __init__(self):
        self.current_file = "accounting_system.xlsx"
        self.workbook = None
        self.load_or_create_workbook()
    
    def load_or_create_workbook(self):
        """تحميل الملف الموجود أو إنشاء ملف جديد"""
        if os.path.exists(self.current_file):
            try:
                self.workbook = openpyxl.load_workbook(self.current_file)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح الملف: {str(e)}")
                self.create_new_workbook()
        else:
            self.create_new_workbook()
    
    def create_new_workbook(self):
        """إنشاء ملف Excel جديد"""
        self.workbook = openpyxl.Workbook()
        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])
        self.save_workbook()
    
    def save_workbook(self):
        """حفظ الملف"""
        try:
            self.workbook.save(self.current_file)
            return True
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف: {str(e)}")
            return False
    def create_account_sheet(self, account_num, account_name, initial_balance=0):
        """إنشاء صفحة حساب جديدة"""
        try:
            sheet_name = f"{account_num}-{account_name}"
            
            # التحقق من عدم وجود الحساب
            if sheet_name in self.workbook.sheetnames:
                messagebox.showerror("خطأ", "هذا الحساب موجود مسبقاً")
                return False
            
            # إنشاء الورقة
            ws = self.workbook.create_sheet(sheet_name)
            ws.sheet_properties.rightToLeft = True  # اتجاه من اليمين لليسار
            
            # إعداد الترويسة
            ws.merge_cells('A1:R1')
            ws['A1'] = "المملكة الأردنية الهاشمية"
            ws['A1'].font = Font(size=14, bold=True)
            ws['A1'].alignment = Alignment(horizontal='center')
            
            ws.merge_cells('A2:R2')
            ws['A2'] = "وزارة الصحة"
            ws['A2'].font = Font(size=12, bold=True)
            ws['A2'].alignment = Alignment(horizontal='center')
            
            ws.merge_cells('A3:R3')
            ws['A3'] = f"حساب: {account_name} - رقم: {account_num}"
            ws['A3'].font = Font(size=12, bold=True)
            ws['A3'].alignment = Alignment(horizontal='center')
            
            ws.merge_cells('A4:R4')
            current_date = datetime.now().strftime("%Y/%m/%d")
            ws['A4'] = f"تاريخ الإنشاء: {current_date}"
            ws['A4'].alignment = Alignment(horizontal='center')
            
            # إعداد الأقسام
            self._setup_sections(ws, initial_balance)
            # حفظ التغييرات
            return self.save_workbook()
            
        except Exception as e:
            messagebox.showerror("خطأ", str(e))
            return False
    def _setup_sections(self, ws, initial_balance):
        """إعداد أقسام الصفحة"""
        # تعريف الحدود
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # إعداد عنوان الأقسام
        ws.merge_cells('A7:R7')
        ws['A7'] = "سجل المستندات والحوالات المالية"
        ws['A7'].font = Font(bold=True)
        ws['A7'].alignment = Alignment(horizontal='center')
        
        for i in range(6):
            col = chr(65 + (i * 3))  # A, D, G, J, M, P
            
            # عناوين الأقسام
            ws.merge_cells(f'{col}6:{chr(ord(col)+2)}6')
            ws[f'{col}6'] = f"القسم {i+1}"
            ws[f'{col}6'].font = Font(bold=True)
            ws[f'{col}6'].alignment = Alignment(horizontal='center')
            ws[f'{col}6'].fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
            
            # العناوين
            ws[f'{col}8'] = "المبلغ"
            ws[f'{chr(ord(col) + 1)}8'] = "مستند الصرف"
            ws[f'{chr(ord(col) + 2)}8'] = "رقم التأدية"
            
            # تنسيق العناوين والخلايا
            for j in range(3):
                col_letter = chr(ord(col) + j)
                cell = ws[f'{col_letter}8']
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
                cell.border = thin_border
                
                # إضافة حدود لجميع الخلايا في العمود
                for row in range(9, 33):
                    cell = ws[f'{col_letter}{row}']
                    cell.border = thin_border
                    cell.alignment = Alignment(horizontal='center')
            
            # القيم الأولية            # إعداد الرصيد الافتتاحي وقيمة ما قبله
            if i == 0:
                ws[f'{col}9'] = initial_balance
                ws[f'{chr(ord(col) + 1)}9'] = "رصيد افتتاحي"
            else:
                ws[f'{col}9'] = f"={chr(ord(col)-3)}33"
                ws[f'{chr(ord(col) + 1)}9'] = "ما قبله"
            
            # إعداد صف المجموع
            ws.merge_cells(f'{col}32:{chr(ord(col)+2)}32')
            ws[f'{col}32'] = "المجموع"
            ws[f'{col}32'].font = Font(bold=True)
            ws[f'{col}32'].fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
            ws[f'{col}32'].alignment = Alignment(horizontal='center')
            
            # صيغة المجموع
            ws[f'{col}33'] = f"=SUM({col}10:{col}31)"
            ws[f'{col}33'].font = Font(bold=True)
            ws[f'{chr(ord(col) + 1)}33'] = "الإجمالي"
            
            # تنسيق خلايا المجموع
            for j in range(3):
                col_letter = chr(ord(col) + j)
                cell = ws[f'{col_letter}33']
                cell.border = Border(
                    top=Side(style='double'),
                    bottom=Side(style='double'),
                    left=Side(style='thin'),
                    right=Side(style='thin')
                )
                cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
                
    def add_document(self, sheet_name, amount, doc_num, pay_num):
        """إضافة مستند إلى حساب"""
        try:
            ws = self.workbook[sheet_name]
            empty_cell = self._find_empty_cell(ws)
            
            if not empty_cell:
                messagebox.showerror("خطأ", "لا يوجد مكان لإضافة مستند جديد في هذا القسم")
                return False
            
            row, col = empty_cell
            
            # التأكد من أن القيمة رقمية
            try:
                amount = float(amount)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيمة رقمية صحيحة للمبلغ")
                return False
                
            # إضافة المستند
            amount_cell = ws.cell(row=row, column=col)
            amount_cell.value = amount
            amount_cell.number_format = '#,##0.00'  # تنسيق المبلغ
            
            doc_cell = ws.cell(row=row, column=col+1)
            doc_cell.value = str(doc_num)
            doc_cell.alignment = Alignment(horizontal='center')
            
            pay_cell = ws.cell(row=row, column=col+2)
            pay_cell.value = str(pay_num)
            pay_cell.alignment = Alignment(horizontal='center')
            
            # تحديث الخلايا المرتبطة
            self.workbook.calculate_only = True
            self.workbook.calculate()
            
            return self.save_workbook()
            
        except Exception as e:
            messagebox.showerror("خطأ", str(e))
            return False
            
    def _find_empty_cell(self, ws):
        """البحث عن خلية فارغة"""
        for i in range(6):  # الأقسام الستة
            col_start = chr(65 + (i * 3))  # A, D, G, J, M, P
            col_num = ord(col_start) - 64  # تحويل الحرف إلى رقم العمود
            
            # البحث عن خلية فارغة في هذا القسم
            for row in range(10, 33):
                cell_value = ws.cell(row=row, column=col_num).value
                # تجاهل الخلايا التي تحتوي على صيغ Excel
                if not cell_value or (isinstance(cell_value, str) and cell_value.startswith('=')):
                    return (row, col_num)
        return None
    
    def create_report(self):
        """إنشاء تقرير المجاميع"""
        if 'التقارير' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['التقارير'])
            
        ws = self.workbook.create_sheet('التقارير')
        ws.sheet_properties.rightToLeft = True
        
        # إعداد العناوين
        headers = ['الحساب', 'الرصيد الافتتاحي', 'مجموع المستندات', 'الرصيد النهائي']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # إضافة الحسابات
        row = 2
        total_opening = 0
        total_documents = 0
        total_final = 0
        
        for sheet_name in self.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                ws_account = self.workbook[sheet_name]
                
                # الرصيد الافتتاحي (أول قيمة في القسم الأول)
                opening_balance = ws_account['A9'].value or 0
                total_opening += opening_balance
                
                # مجموع المستندات (مجموع جميع الأقسام)
                documents_total = 0
                for i in range(6):
                    col = chr(65 + (i * 3))  # A, D, G, J, M, P
                    section_total = ws_account[f'{col}33'].value
                    if isinstance(section_total, (int, float)):
                        documents_total += section_total
                total_documents += documents_total
                
                # الرصيد النهائي
                final_balance = opening_balance + documents_total
                total_final += final_balance
                
                # إضافة الصف
                ws.cell(row=row, column=1).value = sheet_name
                ws.cell(row=row, column=2).value = opening_balance
                ws.cell(row=row, column=3).value = documents_total
                ws.cell(row=row, column=4).value = final_balance
                
                # تنسيق الخلايا
                for col in range(1, 5):
                    ws.cell(row=row, column=col).border = Border(all=Side(style='thin'))
                
                row += 1
        
        # إضافة المجاميع
        ws.cell(row=row, column=1).value = "المجموع"
        ws.cell(row=row, column=2).value = total_opening
        ws.cell(row=row, column=3).value = total_documents
        ws.cell(row=row, column=4).value = total_final
        
        # تنسيق صف المجموع
        for col in range(1, 5):
            cell = ws.cell(row=row, column=col)
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
            cell.fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
        
        # تنسيق عرض الأعمدة
        for col in range(1, 5):
            ws.column_dimensions[chr(64 + col)].width = 20
        
        self.save_workbook()
        return True

    def create_documents_report(self, account_name=None):
        """إنشاء تقرير المستندات"""
        sheet_name = 'تقرير المستندات'
        if sheet_name in self.workbook.sheetnames:
            self.workbook.remove(self.workbook[sheet_name])

        ws = self.workbook.create_sheet(sheet_name)
        ws.sheet_properties.rightToLeft = True

        # إعداد العناوين
        headers = ['الحساب', 'المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.border = Border(all=Side(style='thin'))
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        row = 2
        # تجميع المستندات من كل حساب
        for sheet_name in self.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                if account_name and sheet_name != account_name:
                    continue

                ws_account = self.workbook[sheet_name]
                for section in range(6):  # الأقسام الستة
                    col_start = 1 + (section * 3)
                    for doc_row in range(10, 33):
                        amount = ws_account.cell(row=doc_row, column=col_start).value
                        doc_num = ws_account.cell(row=doc_row, column=col_start+1).value
                        pay_num = ws_account.cell(row=doc_row, column=col_start+2).value

                        if amount and doc_num and pay_num:  # إذا كان هناك مستند
                            ws.cell(row=row, column=1).value = sheet_name
                            ws.cell(row=row, column=2).value = amount
                            ws.cell(row=row, column=3).value = doc_num
                            ws.cell(row=row, column=4).value = pay_num
                            ws.cell(row=row, column=5).value = f"القسم {section + 1}"

                            # تنسيق الخلايا
                            for col in range(1, 6):
                                ws.cell(row=row, column=col).border = Border(all=Side(style='thin'))

                            row += 1

        # تنسيق العرض
        for col in range(1, 6):
            ws.column_dimensions[chr(64 + col)].width = 15

        self.save_workbook()
        return True
