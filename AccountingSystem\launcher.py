#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل النظام المحاسبي - ملف تشغيل محسن
نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية
"""

import sys
import os
import traceback

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")

    # فحص Python version
    if sys.version_info < (3, 7):
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor} غير مدعوم")
        print("يتطلب Python 3.7 أو أحدث")
        return False

    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

    # فحص tkinter
    try:
        import tkinter as tk
        print("✅ tkinter متوفر")
    except ImportError:
        print("❌ tkinter غير متوفر")
        return False

    # فحص openpyxl
    try:
        import openpyxl
        print(f"✅ openpyxl {openpyxl.__version__} متوفر")
    except ImportError:
        print("❌ openpyxl غير مثبت")
        print("يرجى تثبيت openpyxl: pip install openpyxl")
        return False

    # فحص الملفات المطلوبة
    required_files = ['app.py', 'excel_manager.py', 'document_window.py', 'search_window.py']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} مفقود")
            return False

    print("✅ جميع المتطلبات متوفرة")
    return True

def setup_environment():
    """إعداد البيئة"""
    try:
        # تعيين ترميز UTF-8
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')

        # تعيين متغيرات البيئة
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        return True
    except Exception as e:
        print(f"⚠️ تحذير في إعداد البيئة: {e}")
        return True  # نستمر حتى لو فشل الإعداد

def main():
    """تشغيل النظام"""
    print("🚀 بدء تشغيل نظام إدارة المستندات المحاسبية")
    print("=" * 60)

    try:
        # إعداد البيئة
        if not setup_environment():
            print("❌ فشل في إعداد البيئة")
            return False

        # فحص المتطلبات
        if not check_requirements():
            print("❌ فشل في فحص المتطلبات")
            input("اضغط Enter للإغلاق...")
            return False

        print("\n🎯 بدء تشغيل التطبيق...")

        # استيراد المكتبات
        import tkinter as tk
        from tkinter import messagebox

        # استيراد التطبيق
        from app import AccountingApp

        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية")
        root.geometry("1200x700")

        # تعيين الأيقونة إذا كانت متوفرة
        try:
            if os.path.exists("icon.ico"):
                root.iconbitmap("icon.ico")
        except:
            pass  # تجاهل خطأ الأيقونة

        # تعيين الخط العربي
        try:
            root.option_add("*font", "Arial 11")
        except:
            pass

        # إنشاء التطبيق
        print("✅ إنشاء واجهة التطبيق...")
        app = AccountingApp(root)

        print("✅ النظام جاهز للاستخدام!")
        print("=" * 60)

        # تشغيل التطبيق
        root.mainloop()

        print("👋 تم إغلاق النظام بنجاح")
        return True

    except ImportError as e:
        error_msg = f"خطأ في استيراد المكتبات: {str(e)}"
        print(f"❌ {error_msg}")

        try:
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()  # إخفاء النافذة الرئيسية
            messagebox.showerror("خطأ في التشغيل", error_msg)
        except:
            pass

        input("اضغط Enter للإغلاق...")
        return False

    except Exception as e:
        error_msg = f"خطأ غير متوقع: {str(e)}"
        print(f"❌ {error_msg}")
        print("\n📋 تفاصيل الخطأ:")
        traceback.print_exc()

        try:
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()  # إخفاء النافذة الرئيسية
            messagebox.showerror("خطأ في التشغيل",
                               f"{error_msg}\n\nراجع وحدة التحكم للتفاصيل")
        except:
            pass

        input("اضغط Enter للإغلاق...")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ حرج: {str(e)}")
        traceback.print_exc()
        input("اضغط Enter للإغلاق...")
        sys.exit(1)
