#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية
الملف الرئيسي المبسط للتشغيل
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    try:
        # التحقق من المكتبات المطلوبة
        try:
            import openpyxl
        except ImportError:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nيرجى تثبيتها باستخدام: pip install openpyxl")
            return
        
        # استيراد التطبيق
        from app import AccountingApp
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.title("نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية")
        root.geometry("1200x700")
        
        # تعيين الخط العربي
        root.option_add("*font", "Arial 11")
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        
        # تشغيل التطبيق
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"خطأ في استيراد الملفات: {str(e)}"
        messagebox.showerror("خطأ في التشغيل", error_msg)
        print(error_msg)
        
    except Exception as e:
        error_msg = f"حدث خطأ غير متوقع: {str(e)}"
        messagebox.showerror("خطأ", error_msg)
        print(error_msg)

if __name__ == "__main__":
    main()
