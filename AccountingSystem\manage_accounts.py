import tkinter as tk
from tkinter import ttk, messagebox

class ManageAccountsDialog(tk.Toplevel):
    def __init__(self, parent, excel):
        super().__init__(parent)
        self.title("إدارة الحسابات")
        self.excel = excel
        
        # تكوين النافذة
        self.geometry("800x500")
        self.configure(bg='#f0f0f0')
        
        # إطار رئيسي
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # قائمة الحسابات
        self.create_accounts_list(main_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)
        
        # تحديث القائمة
        self.load_accounts()
    
    def create_accounts_list(self, parent):
        # إطار القائمة
        list_frame = ttk.LabelFrame(parent, text="الحسابات", padding="5")
        list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=5)
        
        # إنشاء جدول الحسابات
        columns = ('رقم الحساب', 'اسم الحساب', 'الرصيد')
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show='headings')
        
        # تعيين العناوين
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=150)
        
        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)
        
        # وضع العناصر في الإطار
        self.accounts_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # تمكين التمدد
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
    
    def create_control_buttons(self, parent):
        # إطار الأزرار
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=1, column=0, pady=10)

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.pack(pady=5)

        ttk.Button(row1_frame, text="عرض تفاصيل الحساب",
                  command=self.view_account_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1_frame, text="تعديل الحساب",
                  command=self.edit_account).pack(side=tk.LEFT, padx=5)

        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.pack(pady=5)

        ttk.Button(row2_frame, text="حذف الحساب",
                  command=self.delete_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="تحديث القائمة",
                  command=self.load_accounts).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2_frame, text="إغلاق",
                  command=self.destroy).pack(side=tk.LEFT, padx=5)
    
    def load_accounts(self):
        """تحميل الحسابات في الجدول"""
        # مسح الجدول
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)
        
        # تحميل الحسابات
        for sheet_name in self.excel.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                try:
                    # استخراج رقم واسم الحساب
                    account_num, account_name = sheet_name.split('-', 1)
                    
                    # الحصول على الرصيد
                    ws = self.excel.workbook[sheet_name]
                    balance = ws['A33'].value or 0
                    
                    # إضافة الصف
                    self.accounts_tree.insert('', tk.END, values=(account_num, account_name, balance))
                except:
                    continue

    def view_account_details(self):
        """عرض تفاصيل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لعرض تفاصيله")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # إنشاء نافذة التفاصيل
        details_dialog = AccountDetailsDialog(self, self.excel, sheet_name, account_num, account_name)
        self.wait_window(details_dialog)

    def edit_account(self):
        """تعديل الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لتعديله")
            return
        
        item = selection[0]
        account_num, account_name, _ = self.accounts_tree.item(item)['values']
        
        # إنشاء نافذة التعديل
        dialog = AccountEditDialog(self, self.excel, account_num, account_name)
        self.wait_window(dialog)
        
        # تحديث القائمة
        self.load_accounts()
    
    def delete_account(self):
        """حذف الحساب المحدد"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("تنبيه", "الرجاء اختيار حساب لحذفه")
            return

        item = selection[0]
        account_num, account_name, balance = self.accounts_tree.item(item)['values']
        sheet_name = f"{account_num}-{account_name}"

        # نافذة تأكيد بسيطة وموثوقة
        confirm_message = (f"هل أنت متأكد من حذف الحساب؟\n\n"
                          f"رقم الحساب: {account_num}\n"
                          f"اسم الحساب: {account_name}\n"
                          f"الرصيد الحالي: {balance}\n\n"
                          f"تحذير: سيتم حذف الحساب نهائياً مع جميع المستندات!")

        if messagebox.askyesno("تأكيد حذف الحساب", confirm_message):
            try:
                print(f"🗑️ حذف الحساب: {sheet_name}")  # للتشخيص

                # التحقق من وجود الحساب
                if sheet_name not in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", f"الحساب '{sheet_name}' غير موجود")
                    return

                # حذف الصفحة
                self.excel.workbook.remove(self.excel.workbook[sheet_name])
                print(f"✅ تم حذف الصفحة من الملف")  # للتشخيص

                # تحديث التقرير الإجمالي تلقائياً
                print(f"📊 تحديث التقرير الإجمالي بعد حذف الحساب...")
                self.excel.create_summary_report()

                # حفظ التغييرات
                if self.excel.save_workbook():
                    print(f"✅ تم حفظ الملف بنجاح")  # للتشخيص

                    # تحديث القائمة
                    self.load_accounts()

                    messagebox.showinfo("نجاح", f"تم حذف الحساب '{account_name}' بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ التغييرات")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء حذف الحساب: {str(e)}"
                print(f"❌ {error_msg}")  # للتشخيص
                messagebox.showerror("خطأ", error_msg)

class AccountEditDialog(tk.Toplevel):
    def __init__(self, parent, excel, account_num, account_name):
        super().__init__(parent)
        self.title("تعديل الحساب")
        self.excel = excel
        self.old_sheet_name = f"{account_num}-{account_name}"
        
        # تكوين النافذة
        self.geometry("400x200")
        
        # حقول الإدخال
        ttk.Label(self, text="رقم الحساب:").grid(row=0, column=0, padx=5, pady=5)
        self.account_num = ttk.Entry(self)
        self.account_num.insert(0, account_num)
        self.account_num.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self, text="اسم الحساب:").grid(row=1, column=0, padx=5, pady=5)
        self.account_name = ttk.Entry(self)
        self.account_name.insert(0, account_name)
        self.account_name.grid(row=1, column=1, padx=5, pady=5)
        
        # أزرار
        ttk.Button(self, text="حفظ",
                  command=self.save_changes).grid(row=2, column=0, columnspan=2, pady=20)
    
    def save_changes(self):
        """حفظ التغييرات على الحساب"""
        try:
            new_sheet_name = f"{self.account_num.get()}-{self.account_name.get()}"
            
            if new_sheet_name != self.old_sheet_name:
                # التحقق من عدم وجود حساب بنفس الاسم
                if new_sheet_name in self.excel.workbook.sheetnames:
                    messagebox.showerror("خطأ", "يوجد حساب بنفس الرقم والاسم")
                    return
                
                # تغيير اسم الصفحة
                sheet = self.excel.workbook[self.old_sheet_name]
                sheet.title = new_sheet_name
                
                # حفظ التغييرات
                if self.excel.save_workbook():
                    messagebox.showinfo("نجاح", "تم تعديل الحساب بنجاح")
                    self.destroy()
            else:
                self.destroy()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل الحساب: {str(e)}")

class DeleteConfirmDialog(tk.Toplevel):
    """نافذة تأكيد حذف الحساب"""
    def __init__(self, parent, account_num, account_name, balance, sheet_name):
        super().__init__(parent)
        self.title("تأكيد حذف الحساب")
        self.account_num = account_num
        self.account_name = account_name
        self.balance = balance
        self.sheet_name = sheet_name
        self.confirmed = False

        # تكوين النافذة
        self.geometry("500x300")
        self.configure(bg='#f0f0f0')
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

        # توسيط النافذة
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # أيقونة تحذير
        warning_label = tk.Label(main_frame, text="⚠️", font=("Arial", 48),
                                bg='#f0f0f0', fg='#ff6b6b')
        warning_label.pack(pady=(0, 20))

        # عنوان التحذير
        title_label = tk.Label(main_frame, text="تحذير: حذف الحساب",
                              font=("Arial", 16, "bold"),
                              bg='#f0f0f0', fg='#d63031')
        title_label.pack(pady=(0, 10))

        # معلومات الحساب
        info_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(info_frame, text="معلومات الحساب المراد حذفه:",
                font=("Arial", 12, "bold"), bg='#ffffff').pack(pady=5)

        tk.Label(info_frame, text=f"رقم الحساب: {self.account_num}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"اسم الحساب: {self.account_name}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10)

        tk.Label(info_frame, text=f"الرصيد الحالي: {self.balance}",
                font=("Arial", 11), bg='#ffffff').pack(anchor=tk.W, padx=10, pady=(0, 5))

        # رسالة التحذير
        warning_text = ("سيتم حذف الحساب نهائياً مع جميع المستندات والبيانات المرتبطة به.\n"
                       "هذا الإجراء لا يمكن التراجع عنه!")
        warning_label = tk.Label(main_frame, text=warning_text,
                                font=("Arial", 11), bg='#f0f0f0', fg='#d63031',
                                wraplength=400, justify=tk.CENTER)
        warning_label.pack(pady=(0, 20))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack()

        # زر الحذف
        delete_btn = tk.Button(buttons_frame, text="حذف الحساب",
                              command=self.confirm_delete,
                              bg='#d63031', fg='white',
                              font=("Arial", 11, "bold"),
                              padx=20, pady=5)
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء",
                              command=self.cancel_delete,
                              bg='#74b9ff', fg='white',
                              font=("Arial", 11),
                              padx=20, pady=5)
        cancel_btn.pack(side=tk.LEFT)

        # ربط مفتاح Escape بالإلغاء
        self.bind('<Escape>', lambda e: self.cancel_delete())

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def confirm_delete(self):
        """تأكيد الحذف"""
        self.confirmed = True
        self.destroy()

    def cancel_delete(self):
        """إلغاء الحذف"""
        self.confirmed = False
        self.destroy()

class AccountDetailsDialog(tk.Toplevel):
    """نافذة عرض تفاصيل الحساب"""
    def __init__(self, parent, excel, sheet_name, account_num, account_name):
        super().__init__(parent)
        self.title(f"تفاصيل الحساب: {account_name}")
        self.excel = excel
        self.sheet_name = sheet_name
        self.account_num = account_num
        self.account_name = account_name

        # تكوين النافذة
        self.geometry("700x500")
        self.configure(bg='#f0f0f0')

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.load_account_data()

        # توسيط النافذة
        self.center_window()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات الحساب الأساسية
        info_frame = tk.LabelFrame(main_frame, text="معلومات الحساب",
                                  font=("Arial", 12, "bold"), bg='#f0f0f0')
        info_frame.pack(fill=tk.X, pady=(0, 20))

        # رقم الحساب
        tk.Label(info_frame, text=f"رقم الحساب: {self.account_num}",
                font=("Arial", 11), bg='#f0f0f0').pack(anchor=tk.W, padx=10, pady=5)

        # اسم الحساب
        tk.Label(info_frame, text=f"اسم الحساب: {self.account_name}",
                font=("Arial", 11), bg='#f0f0f0').pack(anchor=tk.W, padx=10, pady=5)

        # الرصيد الافتتاحي
        self.opening_balance_label = tk.Label(info_frame, text="الرصيد الافتتاحي: جاري التحميل...",
                                            font=("Arial", 11), bg='#f0f0f0')
        self.opening_balance_label.pack(anchor=tk.W, padx=10, pady=5)

        # الرصيد الحالي
        self.current_balance_label = tk.Label(info_frame, text="الرصيد الحالي: جاري التحميل...",
                                            font=("Arial", 11, "bold"), bg='#f0f0f0')
        self.current_balance_label.pack(anchor=tk.W, padx=10, pady=5)

        # إحصائيات المستندات
        stats_frame = tk.LabelFrame(main_frame, text="إحصائيات المستندات",
                                   font=("Arial", 12, "bold"), bg='#f0f0f0')
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        self.documents_count_label = tk.Label(stats_frame, text="عدد المستندات: جاري التحميل...",
                                            font=("Arial", 11), bg='#f0f0f0')
        self.documents_count_label.pack(anchor=tk.W, padx=10, pady=5)

        self.total_amount_label = tk.Label(stats_frame, text="إجمالي المبالغ: جاري التحميل...",
                                         font=("Arial", 11), bg='#f0f0f0')
        self.total_amount_label.pack(anchor=tk.W, padx=10, pady=5)

        # قائمة المستندات
        documents_frame = tk.LabelFrame(main_frame, text="المستندات الأخيرة",
                                       font=("Arial", 12, "bold"), bg='#f0f0f0')
        documents_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # جدول المستندات
        columns = ('المبلغ', 'رقم المستند', 'رقم التأدية', 'القسم')
        self.documents_tree = ttk.Treeview(documents_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.documents_tree.heading(col, text=col)
            self.documents_tree.column(col, width=120)

        # شريط التمرير للمستندات
        docs_scrollbar = ttk.Scrollbar(documents_frame, orient=tk.VERTICAL, command=self.documents_tree.yview)
        self.documents_tree.configure(yscrollcommand=docs_scrollbar.set)

        self.documents_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        docs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        # زر الإغلاق
        close_btn = tk.Button(main_frame, text="إغلاق", command=self.destroy,
                             bg='#74b9ff', fg='white', font=("Arial", 11),
                             padx=20, pady=5)
        close_btn.pack()

    def load_account_data(self):
        """تحميل بيانات الحساب"""
        try:
            if self.sheet_name not in self.excel.workbook.sheetnames:
                messagebox.showerror("خطأ", f"الحساب '{self.sheet_name}' غير موجود")
                self.destroy()
                return

            ws = self.excel.workbook[self.sheet_name]

            # الرصيد الافتتاحي (من الخلية A9)
            opening_balance = ws['A9'].value or 0
            self.opening_balance_label.config(text=f"الرصيد الافتتاحي: {opening_balance:,.2f}")

            # حساب الرصيد الحالي وإحصائيات المستندات
            documents_count = 0
            total_amount = 0
            documents_list = []

            # فحص جميع الأقسام الستة
            for section in range(6):
                col_start = 1 + (section * 3)  # A=1, D=4, G=7, J=10, M=13, P=16

                for row in range(10, 32):  # صفوف المستندات
                    amount = ws.cell(row=row, column=col_start).value
                    doc_num = ws.cell(row=row, column=col_start+1).value
                    pay_num = ws.cell(row=row, column=col_start+2).value

                    if amount and doc_num and pay_num:
                        documents_count += 1
                        total_amount += float(amount)
                        documents_list.append((amount, doc_num, pay_num, f"القسم {section + 1}"))

            # الرصيد الحالي
            current_balance = opening_balance + total_amount
            self.current_balance_label.config(text=f"الرصيد الحالي: {current_balance:,.2f}")

            # إحصائيات المستندات
            self.documents_count_label.config(text=f"عدد المستندات: {documents_count}")
            self.total_amount_label.config(text=f"إجمالي المبالغ: {total_amount:,.2f}")

            # تحميل المستندات في الجدول (آخر 20 مستند)
            for doc in documents_list[-20:]:  # آخر 20 مستند
                self.documents_tree.insert('', tk.END, values=doc)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الحساب: {str(e)}")

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')
