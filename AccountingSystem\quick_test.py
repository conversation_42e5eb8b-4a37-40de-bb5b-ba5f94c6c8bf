#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لإصلاح مشكلة calculate
"""

def quick_test():
    """اختبار سريع لإضافة مستند"""
    try:
        print("🧪 اختبار سريع لإضافة مستند...")
        
        # استيراد المكتبات
        from excel_manager import ExcelManager
        
        # إنشاء مدير Excel
        excel = ExcelManager()
        print("✅ تم إنشاء ExcelManager")
        
        # إنشاء حساب تجريبي
        account_num = "QUICK001"
        account_name = "اختبار سريع"
        
        print(f"📝 إنشاء حساب: {account_num} - {account_name}")
        result = excel.create_account_sheet(account_num, account_name, 1000)
        
        if not result:
            print("❌ فشل في إنشاء الحساب")
            return False
        
        print("✅ تم إنشاء الحساب")
        
        # إضافة مستند
        sheet_name = f"{account_num}-{account_name}"
        
        print(f"📄 إضافة مستند للحساب: {sheet_name}")
        result = excel.add_document(sheet_name, 500, "DOC001", "PAY001")
        
        if result:
            print("✅ تم إضافة المستند بنجاح!")
            print("✅ لا توجد أخطاء في calculate()")
            return True
        else:
            print("❌ فشل في إضافة المستند")
            return False
            
    except AttributeError as e:
        if "calculate" in str(e):
            print(f"❌ خطأ calculate لا يزال موجود: {str(e)}")
        else:
            print(f"❌ خطأ AttributeError آخر: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 اختبار سريع لإصلاح مشكلة calculate")
    print("=" * 40)
    
    success = quick_test()
    
    if success:
        print("\n🎉 تم إصلاح المشكلة بنجاح!")
        print("✅ يمكن الآن إضافة المستندات بدون أخطاء")
    else:
        print("\n❌ لا تزال هناك مشكلة")
    
    input("\nاضغط Enter للإغلاق...")
