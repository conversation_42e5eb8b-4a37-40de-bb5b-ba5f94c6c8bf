@echo off
echo Starting Accounting System...
echo.

REM Try different Python commands
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found, starting system...
    python launcher.py
    goto :end
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found via py command, starting system...
    py launcher.py
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python3 found, starting system...
    python3 launcher.py
    goto :end
)

echo ERROR: Python not found!
echo Please install Python from https://www.python.org/downloads/
echo Make sure to check "Add Python to PATH" during installation
echo.

:end
echo.
echo Press any key to exit...
pause >nul
