import tkinter as tk
from tkinter import ttk, messagebox

class SearchWindow(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent.root)
        self.title("البحث في المستندات")
        self.parent = parent
        
        # تكوين النافذة
        self.geometry("700x500")
        self.configure(bg='#f0f0f0')
        
        # إطار البحث
        search_frame = ttk.Frame(self, padding="10")
        search_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # نوع البحث
        ttk.Label(search_frame, text="نوع البحث:").grid(row=0, column=0, padx=5, pady=5)
        self.search_type = ttk.Combobox(search_frame, state='readonly')
        self.search_type['values'] = ["رقم المستند", "رقم التأدية", "قيمة المستند"]
        self.search_type.current(0)
        self.search_type.grid(row=0, column=1, padx=5, pady=5)
        
        # قيمة البحث
        ttk.Label(search_frame, text="قيمة البحث:").grid(row=0, column=2, padx=5, pady=5)
        self.search_value = ttk.Entry(search_frame)
        self.search_value.grid(row=0, column=3, padx=5, pady=5)
        
        # زر البحث
        ttk.Button(search_frame, text="بحث",
                  command=self.perform_search).grid(row=0, column=4, padx=5, pady=5)
        
        # جدول النتائج
        self.create_results_table()
    
    def create_results_table(self):
        """إنشاء جدول النتائج"""
        columns = ('الحساب', 'القيمة', 'رقم المستند', 'رقم التأدية')
        self.tree = ttk.Treeview(self, columns=columns, show='headings')
        
        # تعريف العناوين
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
        
        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # وضع الجدول في النافذة
        self.tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=5)
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        
        # تمكين التمدد
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)
    def perform_search(self):
        """تنفيذ عملية البحث"""
        try:
            # تنظيف النتائج السابقة
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            search_type = self.search_type.get()
            search_value = self.search_value.get().strip()
            
            if not search_value:
                messagebox.showwarning("تنبيه", "الرجاء إدخال قيمة للبحث")
                return
            
            # البحث في كل الصفحات
            excel = self.parent.excel
            workbook = excel.workbook
            
            for sheet_name in workbook.sheetnames:
                ws = workbook[sheet_name]
                
                # البحث في الأقسام الستة
                for i in range(6):
                    col_start = chr(65 + (i * 3))  # A, D, G, J, M, P
                    col_amount = ord(col_start) - 64  # تحويل الحرف إلى رقم العمود
                    col_doc = col_amount + 1  # عمود رقم المستند
                    col_pay = col_amount + 2  # عمود رقم التأدية
                    
                    # البحث في الصفوف من 10 إلى 32
                    for row in range(10, 33):
                        found = False
                        amount = ws.cell(row=row, column=col_amount).value
                        doc_num = ws.cell(row=row, column=col_doc).value
                        pay_num = ws.cell(row=row, column=col_pay).value
                        
                        if search_type == "رقم المستند" and doc_num and str(doc_num) == search_value:
                            found = True
                        elif search_type == "رقم التأدية" and pay_num and str(pay_num) == search_value:
                            found = True
                        elif search_type == "قيمة المستند" and amount:
                            # تحويل القيمة إلى نص للمقارنة
                            if str(amount).strip() == search_value:
                                found = True
                        
                        if found:
                            self.tree.insert('', 'end', values=(
                                sheet_name,
                                amount if amount else '',
                                doc_num if doc_num else '',
                                pay_num if pay_num else ''
                            ))
            
            if not self.tree.get_children():
                messagebox.showinfo("نتيجة البحث", "لم يتم العثور على نتائج")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
