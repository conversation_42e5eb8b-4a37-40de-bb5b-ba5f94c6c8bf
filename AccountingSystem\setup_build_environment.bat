@echo off
chcp 65001 >nul
title Setup Build Environment - Accounting System

echo ========================================
echo    Setup Build Environment
echo    Accounting System
echo ========================================
echo.

echo Setting up build environment...
echo.

REM Use the specific Python path
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists
if exist %PYTHON_PATH% (
    echo Python found at: %PYTHON_PATH%
    echo.
    
    echo Checking Python version...
    %PYTHON_PATH% --version
    echo.
    
    echo Upgrading pip...
    %PYTHON_PATH% -m pip install --upgrade pip
    
    if %errorlevel% neq 0 (
        echo Warning: Failed to upgrade pip, continuing anyway...
    )
    
    echo.
    echo Installing required packages...
    echo.
    
    echo Installing openpyxl...
    %PYTHON_PATH% -m pip install openpyxl>=3.1.0
    
    echo Installing PyInstaller...
    %PYTHON_PATH% -m pip install pyinstaller>=5.13.0
    
    echo Installing Pillow...
    %PYTHON_PATH% -m pip install pillow>=10.0.0
    
    echo.
    echo Verifying installations...
    echo.
    
    echo Testing openpyxl...
    %PYTHON_PATH% -c "import openpyxl; print('openpyxl version:', openpyxl.__version__)"
    
    echo Testing tkinter...
    %PYTHON_PATH% -c "import tkinter; print('tkinter: OK')"
    
    echo Testing PyInstaller...
    %PYTHON_PATH% -c "import PyInstaller; print('PyInstaller: OK')"
    
    if %errorlevel% equ 0 (
        echo.
        echo ========================================
        echo    Setup Successful!
        echo ========================================
        echo.
        echo Build environment is ready.
        echo You can now run: build_executable.bat
        echo.
    ) else (
        echo.
        echo ========================================
        echo    Setup Issues Detected
        echo ========================================
        echo.
        echo Some packages may not have installed correctly.
        echo Please check the error messages above.
        echo.
    )
    
) else (
    echo ERROR: Python not found at expected location
    echo Expected: %PYTHON_PATH%
    echo.
    echo Please install Python 3.7+ from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo.
echo Press any key to exit...
pause >nul
