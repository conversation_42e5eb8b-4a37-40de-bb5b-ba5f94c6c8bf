try:
    import tkinter as tk
    import openpyxl
    from tkinter import ttk
    print("✓ تم استيراد المكتبات بنجاح")
    
    root = tk.Tk()
    root.geometry("400x300")
    root.title("اختبار المكتبات")
    
    # إنشاء عناصر الواجهة
    label = ttk.Label(root, text="اختبار نظام المحاسبة")
    label.pack(pady=20)
    
    # إنشاء ملف Excel للاختبار
    wb = openpyxl.Workbook()
    wb.save("test.xlsx")
    print("✓ تم إنشاء ملف Excel للاختبار")
    
    test_label = ttk.Label(root, text="تم إنشاء ملف Excel بنجاح")
    test_label.pack(pady=20)
    
    root.mainloop()
except Exception as e:
    print(f"حدث خطأ: {str(e)}")
    input("اضغط Enter للإغلاق...")
