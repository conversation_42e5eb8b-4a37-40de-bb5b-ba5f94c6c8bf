#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تنسيق اسم الحساب في صفحة الحساب
التحقق من أن التنسيق يظهر كـ "حساب: 214 - 2" بدلاً من "حساب: 214 - رقم: 2"
"""

import os
import sys

def test_account_format():
    """اختبار تنسيق اسم الحساب"""
    try:
        print("🧪 اختبار تنسيق اسم الحساب...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "214"
        account_name = "حساب اختبار التنسيق"
        balance = 5000
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        print(f"✅ تم إنشاء الحساب: {sheet_name}")
        
        # فحص تنسيق العنوان في الخلية A3
        ws = excel.workbook[sheet_name]
        title_cell = ws['A3'].value
        
        print(f"📋 العنوان في الخلية A3: '{title_cell}'")
        
        # التحقق من التنسيق الصحيح
        expected_format = f"حساب: {account_name} - {account_num}"
        old_format = f"حساب: {account_name} - رقم: {account_num}"
        
        if title_cell == expected_format:
            print("✅ التنسيق صحيح: يظهر 'حساب: ... - ...' بدون كلمة 'رقم:'")
            return True
        elif title_cell == old_format:
            print("❌ التنسيق القديم: لا يزال يظهر 'حساب: ... - رقم: ...'")
            return False
        else:
            print(f"⚠️ تنسيق غير متوقع: '{title_cell}'")
            print(f"   المتوقع: '{expected_format}'")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_accounts():
    """اختبار تنسيق عدة حسابات"""
    try:
        print("\n🧪 اختبار تنسيق عدة حسابات...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء عدة حسابات تجريبية
        test_accounts = [
            ("001", "حساب أول", 1000),
            ("214", "حساب ثاني", 2000),
            ("999", "حساب ثالث", 3000)
        ]
        
        success_count = 0
        
        for account_num, account_name, balance in test_accounts:
            result = excel.create_account_sheet(account_num, account_name, balance)
            if result:
                sheet_name = f"{account_num}-{account_name}"
                ws = excel.workbook[sheet_name]
                title_cell = ws['A3'].value
                
                expected_format = f"حساب: {account_name} - {account_num}"
                
                if title_cell == expected_format:
                    print(f"✅ الحساب {account_num}: التنسيق صحيح")
                    success_count += 1
                else:
                    print(f"❌ الحساب {account_num}: التنسيق خاطئ")
                    print(f"   الموجود: '{title_cell}'")
                    print(f"   المتوقع: '{expected_format}'")
            else:
                print(f"❌ فشل في إنشاء الحساب {account_num}")
        
        if success_count == len(test_accounts):
            print(f"✅ جميع الحسابات ({success_count}/{len(test_accounts)}) لها تنسيق صحيح")
            return True
        else:
            print(f"❌ بعض الحسابات لها تنسيق خاطئ ({success_count}/{len(test_accounts)})")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار عدة حسابات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_format_consistency():
    """اختبار ثبات التنسيق"""
    try:
        print("\n🧪 اختبار ثبات التنسيق...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب
        account_num = "TEST"
        account_name = "حساب اختبار الثبات"
        balance = 1500
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        
        # إضافة مستند
        excel.add_document(sheet_name, 500, "DOC001", "PAY001")
        
        # فحص التنسيق مرة أخرى بعد إضافة المستند
        ws = excel.workbook[sheet_name]
        title_cell = ws['A3'].value
        
        expected_format = f"حساب: {account_name} - {account_num}"
        
        if title_cell == expected_format:
            print("✅ التنسيق ثابت حتى بعد إضافة المستندات")
            return True
        else:
            print("❌ التنسيق تغير بعد إضافة المستندات")
            print(f"   الموجود: '{title_cell}'")
            print(f"   المتوقع: '{expected_format}'")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار ثبات التنسيق: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """اختبار حالات خاصة"""
    try:
        print("\n🧪 اختبار حالات خاصة...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # حالات خاصة
        edge_cases = [
            ("1", "حساب قصير", 100),
            ("123456", "حساب رقم طويل", 200),
            ("ABC", "حساب بحروف", 300),
            ("2024", "حساب سنة", 400)
        ]
        
        success_count = 0
        
        for account_num, account_name, balance in edge_cases:
            result = excel.create_account_sheet(account_num, account_name, balance)
            if result:
                sheet_name = f"{account_num}-{account_name}"
                ws = excel.workbook[sheet_name]
                title_cell = ws['A3'].value
                
                expected_format = f"حساب: {account_name} - {account_num}"
                
                if title_cell == expected_format:
                    print(f"✅ الحالة الخاصة '{account_num}': التنسيق صحيح")
                    success_count += 1
                else:
                    print(f"❌ الحالة الخاصة '{account_num}': التنسيق خاطئ")
                    print(f"   الموجود: '{title_cell}'")
                    print(f"   المتوقع: '{expected_format}'")
            else:
                print(f"❌ فشل في إنشاء الحساب '{account_num}'")
        
        if success_count == len(edge_cases):
            print(f"✅ جميع الحالات الخاصة ({success_count}/{len(edge_cases)}) لها تنسيق صحيح")
            return True
        else:
            print(f"❌ بعض الحالات الخاصة لها تنسيق خاطئ ({success_count}/{len(edge_cases)})")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الحالات الخاصة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار تنسيق اسم الحساب")
    print("=" * 60)
    print("الهدف: التأكد من أن التنسيق يظهر 'حساب: ... - ...' بدلاً من 'حساب: ... - رقم: ...'")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 4
    
    # اختبار التنسيق الأساسي
    if test_account_format():
        success_count += 1
    
    # اختبار عدة حسابات
    if test_multiple_accounts():
        success_count += 1
    
    # اختبار ثبات التنسيق
    if test_format_consistency():
        success_count += 1
    
    # اختبار حالات خاصة
    if test_edge_cases():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات تنسيق اسم الحساب!")
        print("\n✅ التنسيق الآن:")
        print("   قبل الإصلاح: 'حساب: 214 - رقم: 2'")
        print("   بعد الإصلاح: 'حساب: 214 - 2'")
        print("\n✅ تم إزالة كلمة 'رقم:' بنجاح من تنسيق العنوان")
        return True
    else:
        print("❌ فشل في بعض اختبارات تنسيق اسم الحساب")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
