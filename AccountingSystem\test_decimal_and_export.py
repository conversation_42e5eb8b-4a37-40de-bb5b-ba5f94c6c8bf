#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عرض الأعمدة للخانات العشرية وتصدير PDF
"""

import os
import sys
import tkinter as tk

def test_column_width_and_decimals():
    """اختبار عرض الأعمدة وتنسيق الخانات العشرية"""
    try:
        print("🧪 اختبار عرض الأعمدة وتنسيق الخانات العشرية...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "DEC"
        account_name = "حساب اختبار الخانات العشرية"
        balance = 1000.123
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        ws = excel.workbook[sheet_name]
        
        print(f"✅ تم إنشاء الحساب: {sheet_name}")
        
        # التحقق من عرض الأعمدة
        expected_widths = {
            'A': 18, 'B': 20, 'C': 18,  # القسم الأول
            'D': 18, 'E': 20, 'F': 18,  # القسم الثاني
            'G': 18, 'H': 20, 'I': 18,  # القسم الثالث
        }
        
        widths_correct = 0
        for col_letter, expected_width in expected_widths.items():
            actual_width = ws.column_dimensions[col_letter].width
            if actual_width == expected_width:
                print(f"✅ عرض العمود {col_letter}: {actual_width} (صحيح)")
                widths_correct += 1
            else:
                print(f"❌ عرض العمود {col_letter}: {actual_width} (متوقع: {expected_width})")
        
        # إضافة مستندات بخانات عشرية
        test_amounts = [123.456, 789.123, 456.789]
        for i, amount in enumerate(test_amounts):
            doc_num = f"DOC{i+1:03d}"
            pay_num = f"PAY{i+1:03d}"
            excel.add_document(sheet_name, amount, doc_num, pay_num)
            print(f"✅ تم إضافة مستند: {amount}")
        
        # التحقق من تنسيق الأرقام
        print("\n📋 فحص تنسيق الأرقام في الخلايا:")
        for row in range(9, 12):  # فحص أول 3 مستندات
            amount_cell = ws.cell(row=row, column=1)
            if amount_cell.value:
                number_format = amount_cell.number_format
                print(f"   الصف {row}: القيمة={amount_cell.value}, التنسيق={number_format}")
                
                if number_format == '#,##0.000':
                    print(f"   ✅ تنسيق صحيح: ثلاث خانات عشرية")
                else:
                    print(f"   ❌ تنسيق خاطئ: {number_format}")
                    return False
        
        if widths_correct >= 6:  # على الأقل أول قسمين
            print("✅ عرض الأعمدة صحيح")
            return True
        else:
            print(f"❌ عرض الأعمدة خاطئ ({widths_correct}/9)")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأعمدة والخانات العشرية: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_export_functions():
    """اختبار وجود دوال التصدير"""
    try:
        print("\n🧪 اختبار وجود دوال التصدير...")
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        from app import AccountingApp
        app = AccountingApp(root)
        
        # التحقق من وجود دوال التصدير
        export_functions = [
            'export_summary_report',      # تصدير Excel
            'export_summary_report_pdf',  # تصدير PDF
            'generate_pdf_html'           # إنشاء HTML للـ PDF
        ]
        
        functions_found = 0
        for func_name in export_functions:
            if hasattr(app, func_name):
                print(f"✅ دالة {func_name} موجودة")
                functions_found += 1
            else:
                print(f"❌ دالة {func_name} غير موجودة")
        
        root.destroy()
        
        if functions_found == len(export_functions):
            print("✅ جميع دوال التصدير موجودة")
            return True
        else:
            print(f"❌ بعض دوال التصدير مفقودة ({functions_found}/{len(export_functions)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال التصدير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_html_generation():
    """اختبار إنشاء HTML للـ PDF"""
    try:
        print("\n🧪 اختبار إنشاء HTML للـ PDF...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        excel.create_account_sheet("PDF", "حساب اختبار PDF", 1000.123)
        excel.add_document("PDF-حساب اختبار PDF", 500.456, "DOC001", "PAY001")
        
        # إنشاء التقرير
        result = excel.create_summary_report()
        if not result:
            print("❌ فشل في إنشاء التقرير")
            return False
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        from app import AccountingApp
        app = AccountingApp(root)
        
        # اختبار إنشاء HTML للـ PDF
        ws = excel.workbook['التقرير الإجمالي']
        pdf_html = app.generate_pdf_html(ws)
        
        # التحقق من محتوى HTML للـ PDF
        pdf_checks = [
            '@page',                    # إعدادات الصفحة للطباعة
            'size: A4',                # حجم الصفحة
            'margin: 1cm',             # الهوامش
            'font-family',             # الخط
            'border-collapse',         # تنسيق الجدول
            'background-color: #366092', # لون الترويسة
            '@media print',            # إعدادات الطباعة
            'تم إنشاء هذا التقرير',    # النص السفلي
            'حفظ كـ PDF'               # تعليمات PDF
        ]
        
        checks_passed = 0
        for check in pdf_checks:
            if check in pdf_html:
                print(f"✅ عنصر PDF موجود: {check}")
                checks_passed += 1
            else:
                print(f"❌ عنصر PDF مفقود: {check}")
        
        # التحقق من تنسيق الأرقام في HTML
        if ',.3f' in pdf_html or 'toFixed(3)' in pdf_html:
            print("✅ تنسيق الأرقام بثلاث خانات عشرية في PDF")
            checks_passed += 1
        else:
            print("❌ تنسيق الأرقام بثلاث خانات عشرية مفقود في PDF")
        
        root.destroy()
        
        if checks_passed >= 8:  # معظم العناصر موجودة
            print("✅ محتوى HTML للـ PDF صحيح ومكتمل")
            return True
        else:
            print(f"❌ محتوى HTML للـ PDF ناقص ({checks_passed}/{len(pdf_checks)+1})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار HTML للـ PDF: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_report_decimal_formatting():
    """اختبار تنسيق الخانات العشرية في التقارير"""
    try:
        print("\n🧪 اختبار تنسيق الخانات العشرية في التقارير...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي بقيم عشرية
        account_num = "RPT"
        account_name = "حساب اختبار التقرير"
        opening_balance = 1500.789
        
        excel.create_account_sheet(account_num, account_name, opening_balance)
        sheet_name = f"{account_num}-{account_name}"
        
        # إضافة مستندات بقيم عشرية
        documents = [
            (123.456, "DOC001", "PAY001"),
            (789.123, "DOC002", "PAY002"),
            (456.789, "DOC003", "PAY003")
        ]
        
        for amount, doc_num, pay_num in documents:
            excel.add_document(sheet_name, amount, doc_num, pay_num)
        
        # إنشاء التقرير الإجمالي
        result = excel.create_summary_report()
        if not result:
            print("❌ فشل في إنشاء التقرير")
            return False
        
        # فحص تنسيق الأرقام في التقرير
        ws_report = excel.workbook['التقرير الإجمالي']
        
        # البحث عن الحساب في التقرير
        found_account = False
        for row in range(7, ws_report.max_row + 1):
            if ws_report.cell(row=row, column=1).value == account_num:
                found_account = True
                
                # فحص تنسيق الأرقام في أعمدة المبالغ
                for col in [3, 4, 5]:  # أعمدة المبالغ
                    cell = ws_report.cell(row=row, column=col)
                    number_format = cell.number_format
                    
                    print(f"📋 العمود {col}: القيمة={cell.value}, التنسيق={number_format}")
                    
                    if number_format == '#,##0.000':
                        print(f"✅ تنسيق العمود {col} صحيح: ثلاث خانات عشرية")
                    else:
                        print(f"❌ تنسيق العمود {col} خاطئ: {number_format}")
                        return False
                
                break
        
        if not found_account:
            print("❌ لم يتم العثور على الحساب في التقرير")
            return False
        
        print("✅ تنسيق الخانات العشرية في التقرير صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تنسيق التقرير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_export_buttons():
    """اختبار أزرار التصدير في الواجهة"""
    try:
        print("\n🧪 اختبار أزرار التصدير في الواجهة...")
        
        # فحص كود الواجهة
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # التحقق من وجود أزرار التصدير
        ui_checks = [
            'تصدير Excel',              # نص زر Excel
            'تصدير PDF',               # نص زر PDF
            'export_summary_report(',   # استدعاء دالة Excel
            'export_summary_report_pdf(', # استدعاء دالة PDF
            'ttk.Button',              # استخدام أزرار tkinter
        ]
        
        checks_passed = 0
        for check in ui_checks:
            if check in app_content:
                print(f"✅ عنصر واجهة موجود: {check}")
                checks_passed += 1
            else:
                print(f"❌ عنصر واجهة مفقود: {check}")
        
        if checks_passed == len(ui_checks):
            print("✅ أزرار التصدير موجودة في الواجهة")
            return True
        else:
            print(f"❌ بعض أزرار التصدير مفقودة ({checks_passed}/{len(ui_checks)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة التصدير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار الخانات العشرية والتصدير")
    print("=" * 60)
    print("الأهداف:")
    print("1. التحقق من عرض الأعمدة لثلاث خانات عشرية")
    print("2. التحقق من تنسيق الأرقام بثلاث خانات عشرية")
    print("3. التحقق من دوال تصدير Excel و PDF")
    print("4. التحقق من إنشاء HTML للـ PDF")
    print("5. التحقق من أزرار التصدير في الواجهة")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 5
    
    # اختبار عرض الأعمدة والخانات العشرية
    if test_column_width_and_decimals():
        success_count += 1
    
    # اختبار دوال التصدير
    if test_export_functions():
        success_count += 1
    
    # اختبار إنشاء HTML للـ PDF
    if test_pdf_html_generation():
        success_count += 1
    
    # اختبار تنسيق التقارير
    if test_report_decimal_formatting():
        success_count += 1
    
    # اختبار أزرار الواجهة
    if test_ui_export_buttons():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات الخانات العشرية والتصدير!")
        print("\n✅ المميزات المختبرة:")
        print("   - عرض الأعمدة يتسع لثلاث خانات عشرية")
        print("   - تنسيق الأرقام بثلاث خانات عشرية")
        print("   - تصدير Excel مع التنسيق الصحيح")
        print("   - تصدير PDF مع HTML محسن")
        print("   - أزرار التصدير في الواجهة")
        
        print("\n🔧 للاستخدام:")
        print("   1. شغل النظام: run_simple.bat")
        print("   2. أضف حسابات ومستندات بقيم عشرية")
        print("   3. أنشئ التقرير الإجمالي")
        print("   4. استخدم أزرار 'تصدير Excel' و 'تصدير PDF'")
        
        print("\n📋 ملاحظات:")
        print("   - الأرقام تظهر بثلاث خانات عشرية (123.456)")
        print("   - الأعمدة واسعة بما يكفي لعرض الأرقام")
        print("   - تصدير PDF يتطلب متصفح ويب")
        
        return True
    else:
        print("❌ فشل في بعض اختبارات الخانات العشرية والتصدير")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
