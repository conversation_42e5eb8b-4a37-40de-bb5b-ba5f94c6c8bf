#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظيفة حذف الحساب
"""

import os
import sys

def test_delete_account_functionality():
    """اختبار وظيفة حذف الحساب"""
    try:
        print("🧪 اختبار وظيفة حذف الحساب...")
        
        # استيراد المكتبات
        from excel_manager import ExcelManager
        
        # إنشاء مدير Excel
        excel = ExcelManager()
        print("✅ تم إنشاء ExcelManager")
        
        # إنشاء حسابات تجريبية
        test_accounts = [
            ("DEL001", "حساب للحذف 1", 1000),
            ("DEL002", "حساب للحذف 2", 2000),
            ("KEEP001", "حساب للاحتفاظ", 5000)
        ]
        
        print(f"📝 إنشاء {len(test_accounts)} حسابات تجريبية...")
        created_accounts = []
        
        for account_num, account_name, balance in test_accounts:
            result = excel.create_account_sheet(account_num, account_name, balance)
            if result:
                sheet_name = f"{account_num}-{account_name}"
                created_accounts.append(sheet_name)
                print(f"✅ تم إنشاء الحساب: {sheet_name}")
            else:
                print(f"❌ فشل في إنشاء الحساب: {account_num}-{account_name}")
        
        print(f"✅ تم إنشاء {len(created_accounts)} حساب بنجاح")
        
        # إضافة مستندات للحسابات
        print(f"📄 إضافة مستندات للحسابات...")
        for i, sheet_name in enumerate(created_accounts[:2]):  # أول حسابين فقط
            for j in range(3):  # 3 مستندات لكل حساب
                amount = 100 * (i + 1) * (j + 1)
                doc_num = f"DOC{i+1}{j+1:02d}"
                pay_num = f"PAY{i+1}{j+1:02d}"
                
                result = excel.add_document(sheet_name, amount, doc_num, pay_num)
                if result:
                    print(f"✅ تم إضافة مستند {doc_num} للحساب {sheet_name}")
                else:
                    print(f"❌ فشل في إضافة مستند {doc_num}")
        
        # عرض الحسابات قبل الحذف
        print(f"\n📊 الحسابات الموجودة قبل الحذف:")
        for sheet_name in excel.workbook.sheetnames:
            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                print(f"   - {sheet_name}")
        
        # اختبار حذف حساب
        account_to_delete = created_accounts[0]  # أول حساب
        print(f"\n🗑️ اختبار حذف الحساب: {account_to_delete}")
        
        # التحقق من وجود الحساب قبل الحذف
        if account_to_delete in excel.workbook.sheetnames:
            print(f"✅ الحساب موجود قبل الحذف")
            
            # حذف الحساب
            try:
                excel.workbook.remove(excel.workbook[account_to_delete])
                print(f"✅ تم حذف الحساب من الذاكرة")
                
                # حفظ التغييرات
                if excel.save_workbook():
                    print(f"✅ تم حفظ التغييرات")
                    
                    # التحقق من عدم وجود الحساب بعد الحذف
                    if account_to_delete not in excel.workbook.sheetnames:
                        print(f"✅ تم التأكد من حذف الحساب")
                        
                        # عرض الحسابات بعد الحذف
                        print(f"\n📊 الحسابات الموجودة بعد الحذف:")
                        for sheet_name in excel.workbook.sheetnames:
                            if sheet_name not in ['التقارير', 'تقرير المستندات']:
                                print(f"   - {sheet_name}")
                        
                        print(f"✅ اختبار حذف الحساب نجح!")
                        return True
                    else:
                        print(f"❌ الحساب لا يزال موجود بعد الحذف")
                        return False
                else:
                    print(f"❌ فشل في حفظ التغييرات")
                    return False
                    
            except Exception as e:
                print(f"❌ خطأ أثناء حذف الحساب: {str(e)}")
                return False
        else:
            print(f"❌ الحساب غير موجود قبل الحذف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_delete_account_with_documents():
    """اختبار حذف حساب يحتوي على مستندات"""
    try:
        print("\n🧪 اختبار حذف حساب يحتوي على مستندات...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "DELTEST"
        account_name = "حساب اختبار الحذف"
        balance = 10000
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        print(f"✅ تم إنشاء الحساب: {sheet_name}")
        
        # إضافة عدة مستندات
        documents = [
            (1500, "DOC001", "PAY001"),
            (2000, "DOC002", "PAY002"),
            (1000, "DOC003", "PAY003"),
            (3000, "DOC004", "PAY004"),
            (500, "DOC005", "PAY005")
        ]
        
        print(f"📄 إضافة {len(documents)} مستندات...")
        for amount, doc_num, pay_num in documents:
            result = excel.add_document(sheet_name, amount, doc_num, pay_num)
            if result:
                print(f"✅ تم إضافة مستند {doc_num}")
            else:
                print(f"❌ فشل في إضافة مستند {doc_num}")
        
        # التحقق من وجود المستندات
        ws = excel.workbook[sheet_name]
        documents_found = 0
        
        for row in range(10, 32):
            amount = ws.cell(row=row, column=1).value  # العمود A
            if amount:
                documents_found += 1
        
        print(f"📊 تم العثور على {documents_found} مستندات في الحساب")
        
        # حذف الحساب مع جميع المستندات
        print(f"🗑️ حذف الحساب مع جميع المستندات...")
        
        try:
            excel.workbook.remove(excel.workbook[sheet_name])
            
            if excel.save_workbook():
                print(f"✅ تم حذف الحساب مع جميع المستندات بنجاح")
                
                # التحقق من عدم وجود الحساب
                if sheet_name not in excel.workbook.sheetnames:
                    print(f"✅ تم التأكد من حذف الحساب نهائياً")
                    return True
                else:
                    print(f"❌ الحساب لا يزال موجود")
                    return False
            else:
                print(f"❌ فشل في حفظ التغييرات")
                return False
                
        except Exception as e:
            print(f"❌ خطأ أثناء حذف الحساب: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار وظيفة حذف الحساب")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 2
    
    # اختبار حذف حساب عادي
    if test_delete_account_functionality():
        success_count += 1
    
    # اختبار حذف حساب مع مستندات
    if test_delete_account_with_documents():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات حذف الحساب!")
        print("✅ وظيفة حذف الحساب تعمل بشكل صحيح")
        return True
    else:
        print("❌ فشل في بعض اختبارات حذف الحساب")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
