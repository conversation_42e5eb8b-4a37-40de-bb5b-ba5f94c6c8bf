#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أزرار حذف الحسابات الجديدة
"""

import os
import sys
import tkinter as tk

def test_delete_buttons_exist():
    """اختبار وجود أزرار الحذف الجديدة"""
    try:
        print("🧪 اختبار وجود أزرار الحذف الجديدة...")
        
        # فحص كود ملف إدارة الحسابات
        with open('manage_accounts.py', 'r', encoding='utf-8') as f:
            manage_content = f.read()
        
        # التحقق من وجود النصوص والدوال
        required_elements = [
            'حذف الحسابات المحددة',           # نص الزر
            'حذف جميع الحسابات',              # نص الزر
            'delete_selected_accounts',       # اسم الدالة
            'delete_all_accounts',            # اسم الدالة
            'DeleteAllAccountsDialog',        # نافذة التأكيد
            'selectmode=\'extended\'',        # التحديد المتعدد
            'selection()',                   # استخدام التحديد
        ]
        
        elements_found = 0
        for element in required_elements:
            if element in manage_content:
                print(f"✅ عنصر موجود: {element}")
                elements_found += 1
            else:
                print(f"❌ عنصر مفقود: {element}")
        
        if elements_found == len(required_elements):
            print("✅ جميع عناصر أزرار الحذف موجودة")
            return True
        else:
            print(f"❌ بعض عناصر أزرار الحذف مفقودة ({elements_found}/{len(required_elements)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أزرار الحذف: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_delete_functions():
    """اختبار وجود دوال الحذف"""
    try:
        print("\n🧪 اختبار وجود دوال الحذف...")
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        from manage_accounts import ManageAccountsDialog
        
        # إنشاء نافذة إدارة الحسابات
        manage_dialog = ManageAccountsDialog(root, excel)
        manage_dialog.withdraw()  # إخفاء النافذة
        
        # التحقق من وجود الدوال
        delete_functions = [
            'delete_account',           # حذف حساب واحد
            'delete_selected_accounts', # حذف الحسابات المحددة
            'delete_all_accounts'       # حذف جميع الحسابات
        ]
        
        functions_found = 0
        for func_name in delete_functions:
            if hasattr(manage_dialog, func_name):
                print(f"✅ دالة {func_name} موجودة")
                functions_found += 1
            else:
                print(f"❌ دالة {func_name} غير موجودة")
        
        manage_dialog.destroy()
        root.destroy()
        
        if functions_found == len(delete_functions):
            print("✅ جميع دوال الحذف موجودة")
            return True
        else:
            print(f"❌ بعض دوال الحذف مفقودة ({functions_found}/{len(delete_functions)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال الحذف: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_selection():
    """اختبار التحديد المتعدد في الجدول"""
    try:
        print("\n🧪 اختبار التحديد المتعدد في الجدول...")
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حسابات تجريبية
        test_accounts = [
            ("001", "حساب أول", 1000),
            ("002", "حساب ثاني", 2000),
            ("003", "حساب ثالث", 1500)
        ]
        
        for account_num, account_name, balance in test_accounts:
            excel.create_account_sheet(account_num, account_name, balance)
        
        from manage_accounts import ManageAccountsDialog
        
        # إنشاء نافذة إدارة الحسابات
        manage_dialog = ManageAccountsDialog(root, excel)
        manage_dialog.withdraw()
        
        # التحقق من إعداد التحديد المتعدد
        tree = manage_dialog.accounts_tree
        
        # فحص نوع التحديد
        select_mode = tree.cget('selectmode')
        print(f"📋 نوع التحديد: {select_mode}")
        
        if select_mode == 'extended':
            print("✅ التحديد المتعدد مفعل")
            
            # محاولة تحديد عدة عناصر (محاكاة)
            items = tree.get_children()
            if len(items) >= 2:
                print(f"📋 عدد العناصر المتاحة: {len(items)}")
                print("✅ يمكن تحديد عدة حسابات")
                result = True
            else:
                print("⚠️ عدد الحسابات قليل للاختبار")
                result = True  # لا يزال الإعداد صحيح
        else:
            print(f"❌ التحديد المتعدد غير مفعل: {select_mode}")
            result = False
        
        manage_dialog.destroy()
        root.destroy()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديد المتعدد: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_confirmation_dialogs():
    """اختبار وجود نوافذ التأكيد"""
    try:
        print("\n🧪 اختبار وجود نوافذ التأكيد...")
        
        # فحص كود ملف إدارة الحسابات
        with open('manage_accounts.py', 'r', encoding='utf-8') as f:
            manage_content = f.read()
        
        # التحقق من وجود نوافذ التأكيد
        confirmation_elements = [
            'DeleteAllAccountsDialog',        # نافذة تأكيد حذف الكل
            'messagebox.askyesno',           # نوافذ تأكيد
            'تأكيد حذف',                    # نصوص التأكيد
            'هل أنت متأكد',                 # نصوص التأكيد
            'لا يمكن التراجع',              # تحذيرات
            'تحذير خطير',                  # تحذيرات قوية
            'نسخة احتياطية',               # تذكير بالنسخ الاحتياطية
        ]
        
        elements_found = 0
        for element in confirmation_elements:
            if element in manage_content:
                print(f"✅ عنصر تأكيد موجود: {element}")
                elements_found += 1
            else:
                print(f"❌ عنصر تأكيد مفقود: {element}")
        
        if elements_found >= 5:  # معظم العناصر موجودة
            print("✅ نوافذ التأكيد موجودة ومناسبة")
            return True
        else:
            print(f"❌ نوافذ التأكيد ناقصة ({elements_found}/{len(confirmation_elements)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نوافذ التأكيد: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_layout():
    """اختبار تخطيط الواجهة"""
    try:
        print("\n🧪 اختبار تخطيط الواجهة...")
        
        # فحص كود ملف إدارة الحسابات
        with open('manage_accounts.py', 'r', encoding='utf-8') as f:
            manage_content = f.read()
        
        # التحقق من تنظيم الأزرار
        layout_elements = [
            'row1_frame',                    # الصف الأول من الأزرار
            'row2_frame',                    # الصف الثاني من الأزرار
            'row3_frame',                    # الصف الثالث من الأزرار
            'pack(side=tk.LEFT',            # ترتيب الأزرار
            'padx=5',                       # المسافات بين الأزرار
            'pady=5',                       # المسافات العمودية
        ]
        
        elements_found = 0
        for element in layout_elements:
            if element in manage_content:
                print(f"✅ عنصر تخطيط موجود: {element}")
                elements_found += 1
            else:
                print(f"❌ عنصر تخطيط مفقود: {element}")
        
        if elements_found >= 4:  # معظم عناصر التخطيط موجودة
            print("✅ تخطيط الواجهة منظم")
            return True
        else:
            print(f"❌ تخطيط الواجهة غير منظم ({elements_found}/{len(layout_elements)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تخطيط الواجهة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار أزرار حذف الحسابات الجديدة")
    print("=" * 60)
    print("الأهداف:")
    print("1. التحقق من وجود أزرار الحذف الجديدة")
    print("2. التحقق من وجود دوال الحذف")
    print("3. التحقق من التحديد المتعدد")
    print("4. التحقق من نوافذ التأكيد")
    print("5. التحقق من تخطيط الواجهة")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 5
    
    # اختبار وجود أزرار الحذف
    if test_delete_buttons_exist():
        success_count += 1
    
    # اختبار دوال الحذف
    if test_delete_functions():
        success_count += 1
    
    # اختبار التحديد المتعدد
    if test_multi_selection():
        success_count += 1
    
    # اختبار نوافذ التأكيد
    if test_confirmation_dialogs():
        success_count += 1
    
    # اختبار تخطيط الواجهة
    if test_ui_layout():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات أزرار الحذف!")
        print("\n✅ المميزات المختبرة:")
        print("   - زر حذف الحسابات المحددة")
        print("   - زر حذف جميع الحسابات")
        print("   - التحديد المتعدد في الجدول")
        print("   - نوافذ تأكيد متقدمة")
        print("   - تخطيط واجهة منظم")
        
        print("\n🔧 للاستخدام:")
        print("   1. شغل النظام: run_simple.bat")
        print("   2. اذهب إلى: الحسابات → إدارة الحسابات")
        print("   3. حدد حساب واحد أو أكثر:")
        print("      - Ctrl+Click: تحديد متعدد")
        print("      - Shift+Click: تحديد نطاق")
        print("   4. استخدم أزرار الحذف:")
        print("      - حذف الحساب: حذف المحدد فقط")
        print("      - حذف الحسابات المحددة: حذف جميع المحددة")
        print("      - حذف جميع الحسابات: حذف الكل")
        
        print("\n⚠️ تحذيرات مهمة:")
        print("   - جميع عمليات الحذف نهائية")
        print("   - ستظهر نوافذ تأكيد متعددة")
        print("   - يُنصح بعمل نسخة احتياطية")
        
        return True
    else:
        print("❌ فشل في بعض اختبارات أزرار الحذف")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
