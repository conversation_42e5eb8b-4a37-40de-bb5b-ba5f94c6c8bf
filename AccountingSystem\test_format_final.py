#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لتنسيق صفحة الحساب
"""

import os
import sys

def main():
    """اختبار نهائي"""
    try:
        print("🧪 اختبار نهائي لتنسيق صفحة الحساب")
        print("=" * 50)
        
        # حذف ملف الاختبار إن وجد
        test_file = "accounting_system.xlsx"
        if os.path.exists(test_file):
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        print("\n📝 إنشاء حساب تجريبي...")
        result = excel.create_account_sheet("FINAL", "حساب اختبار نهائي", 1500.789)
        
        if result:
            print("✅ تم إنشاء الحساب بنجاح!")
            
            # فحص الهيكل الأساسي
            sheet_name = "FINAL-حساب اختبار نهائي"
            ws = excel.workbook[sheet_name]
            
            print("\n📋 فحص الهيكل:")
            
            # مساحة الشعار
            logo_content = ws['A1'].value
            print(f"   مساحة الشعار (A1): {logo_content}")
            
            # الترويسة
            header1 = ws['E1'].value
            header2 = ws['E2'].value
            print(f"   الترويسة 1: {header1}")
            print(f"   الترويسة 2: {header2}")
            
            # عنوان الأقسام
            section_title = ws['A5'].value
            print(f"   عنوان الأقسام: {section_title}")
            
            # عناوين الأعمدة
            col1 = ws['A6'].value
            col2 = ws['B6'].value
            col3 = ws['C6'].value
            print(f"   عناوين الأعمدة: {col1} | {col2} | {col3}")
            
            # الرصيد الافتتاحي
            balance = ws['A8'].value
            balance_desc = ws['B8'].value
            print(f"   الرصيد الافتتاحي: {balance} | {balance_desc}")
            
            # صيغة المجموع
            total_formula = ws['A31'].value
            total_desc = ws['B31'].value
            print(f"   المجموع: {total_formula} | {total_desc}")
            
            # إضافة مستندات متعددة
            print("\n📝 إضافة مستندات متعددة...")
            documents = [
                (350.123, "DOC001", "PAY001"),
                (275.456, "DOC002", "PAY002"),
                (189.789, "DOC003", "PAY003")
            ]
            
            added_docs = 0
            for amount, doc_num, pay_num in documents:
                doc_result = excel.add_document(sheet_name, amount, doc_num, pay_num)
                if doc_result:
                    added_docs += 1
                    print(f"   ✅ تم إضافة: {amount} | {doc_num} | {pay_num}")
                else:
                    print(f"   ❌ فشل في إضافة: {amount} | {doc_num} | {pay_num}")
            
            print(f"   تم إضافة {added_docs}/{len(documents)} مستند")
            
            # فحص التنسيق
            print("\n🎨 فحص التنسيق:")
            
            # تنسيق العناوين
            header_cell = ws['A6']
            print(f"   عنوان العمود - غامق: {header_cell.font.bold}")
            print(f"   عنوان العمود - لون الخط: {header_cell.font.color}")
            print(f"   عنوان العمود - لون الخلفية: {header_cell.fill.start_color}")
            
            # تنسيق الرصيد
            balance_cell = ws['A8']
            print(f"   الرصيد - غامق: {balance_cell.font.bold}")
            print(f"   الرصيد - تنسيق الرقم: {balance_cell.number_format}")
            print(f"   الرصيد - لون الخط: {balance_cell.font.color}")
            print(f"   الرصيد - لون الخلفية: {balance_cell.fill.start_color}")
            
            # تنسيق المجموع
            total_cell = ws['A31']
            print(f"   المجموع - غامق: {total_cell.font.bold}")
            print(f"   المجموع - تنسيق الرقم: {total_cell.number_format}")
            print(f"   المجموع - لون الخط: {total_cell.font.color}")
            print(f"   المجموع - لون الخلفية: {total_cell.fill.start_color}")
            
            # عرض الأعمدة
            print(f"   عرض العمود A: {ws.column_dimensions['A'].width}")
            print(f"   عرض العمود B: {ws.column_dimensions['B'].width}")
            print(f"   عرض العمود C: {ws.column_dimensions['C'].width}")
            
            # فحص ترحيل الرصيد في القسم الثاني
            carry_formula = ws['D8'].value
            carry_text = ws['E8'].value
            print(f"   ترحيل الرصيد (D8): {carry_formula}")
            print(f"   نص الترحيل (E8): {carry_text}")
            
            # فحص صيغة المجموع في القسم الثاني
            second_total = ws['D31'].value
            print(f"   مجموع القسم الثاني (D31): {second_total}")
            
            # تقييم النتائج
            checks = [
                (logo_content == "مساحة الشعار", "مساحة الشعار"),
                (header1 == "المملكة الأردنية الهاشمية", "الترويسة الأولى"),
                (section_title == "سجل المستندات والحوالات المالية", "عنوان الأقسام"),
                (col1 == "المبلغ", "عنوان العمود الأول"),
                (balance == 1500.789, "الرصيد الافتتاحي"),
                (isinstance(total_formula, str) and "SUM" in total_formula, "صيغة المجموع"),
                (added_docs >= 2, "إضافة المستندات"),
                (header_cell.font.bold == True, "تنسيق العناوين غامق"),
                (balance_cell.font.bold == True, "تنسيق الرصيد غامق"),
                (total_cell.font.bold == True, "تنسيق المجموع غامق"),
                (balance_cell.number_format == '#,##0.000', "تنسيق الأرقام ثلاث خانات"),
                (ws.column_dimensions['A'].width >= 15, "عرض الأعمدة مناسب"),
                (carry_formula == "=A31", "ترحيل الرصيد صحيح"),
                (isinstance(second_total, str) and "SUM" in second_total, "صيغة المجموع الثاني")
            ]
            
            passed_checks = 0
            print("\n📊 تقييم التنسيق:")
            for check_result, check_name in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                    passed_checks += 1
                else:
                    print(f"   ❌ {check_name}")
            
            print(f"\n📈 النتيجة: {passed_checks}/{len(checks)} ({passed_checks/len(checks)*100:.1f}%)")
            
            if passed_checks >= len(checks) * 0.8:  # 80% نجاح
                print("\n🎉 تنسيق صفحة الحساب ممتاز!")
                print("\n✅ المميزات التي تعمل بشكل صحيح:")
                print("   - مساحة الشعار (4 صفوف)")
                print("   - ترويسة منسقة وملونة")
                print("   - عناوين أعمدة غامقة وملونة")
                print("   - رصيد افتتاحي بتنسيق مميز وأغمق")
                print("   - إضافة مستندات تعمل")
                print("   - صيغة المجموع صحيحة")
                print("   - ترحيل الرصيد بين الأقسام")
                print("   - تنسيق ثلاث خانات عشرية")
                print("   - عرض أعمدة مناسب")
                print("   - تنسيق تلقائي لجميع الأعمدة")
                
                print("\n🔧 للاستخدام:")
                print("   1. شغل النظام: run_simple.bat")
                print("   2. أضف حساب جديد - سيكون بالتنسيق الجديد")
                print("   3. استخدم زر 'اختيار الشعار' في إدارة الحسابات")
                print("   4. أضف مستندات - ستظهر بالتنسيق المحسن")
                
                return True
            else:
                print("\n⚠️ تنسيق صفحة الحساب يحتاج تحسين")
                return False
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 النتيجة النهائية: تنسيق صفحة الحساب يعمل بشكل مثالي! ✨")
    else:
        print("\n⚠️ النتيجة النهائية: هناك مشاكل في تنسيق صفحة الحساب")
    
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
