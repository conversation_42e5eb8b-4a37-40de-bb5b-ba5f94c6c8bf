#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة الشعار والتنسيق المحسن
"""

import os
import sys
import tkinter as tk

def test_logo_button_exists():
    """اختبار وجود زر اختيار الشعار"""
    try:
        print("🧪 اختبار وجود زر اختيار الشعار...")
        
        # فحص كود ملف إدارة الحسابات
        with open('manage_accounts.py', 'r', encoding='utf-8') as f:
            manage_content = f.read()
        
        # التحقق من وجود العناصر المطلوبة
        required_elements = [
            'اختيار الشعار',                # نص الزر
            'select_logo',                  # اسم الدالة
            'filedialog.askopenfilename',   # حوار اختيار الملف
            'LogoConfirmDialog',            # نافذة التأكيد
            'LogoProgressDialog',           # نافذة التقدم
            'apply_logo_to_all_accounts',   # دالة تطبيق الشعار
            'openpyxl.drawing',             # استيراد الصور
            'Image(',                       # استخدام الصور
        ]
        
        elements_found = 0
        for element in required_elements:
            if element in manage_content:
                print(f"✅ عنصر موجود: {element}")
                elements_found += 1
            else:
                print(f"❌ عنصر مفقود: {element}")
        
        if elements_found >= 6:  # معظم العناصر موجودة
            print("✅ زر اختيار الشعار والدوال المرتبطة موجودة")
            return True
        else:
            print(f"❌ بعض عناصر الشعار مفقودة ({elements_found}/{len(required_elements)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار زر الشعار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_logo_functions():
    """اختبار وجود دوال الشعار"""
    try:
        print("\n🧪 اختبار وجود دوال الشعار...")
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        from manage_accounts import ManageAccountsDialog
        
        # إنشاء نافذة إدارة الحسابات
        manage_dialog = ManageAccountsDialog(root, excel)
        manage_dialog.withdraw()
        
        # التحقق من وجود دوال الشعار
        logo_functions = [
            'select_logo',                  # اختيار الشعار
            'apply_logo_to_all_accounts',   # تطبيق الشعار
            'remove_existing_logo'          # حذف الشعار القديم
        ]
        
        functions_found = 0
        for func_name in logo_functions:
            if hasattr(manage_dialog, func_name):
                print(f"✅ دالة {func_name} موجودة")
                functions_found += 1
            else:
                print(f"❌ دالة {func_name} غير موجودة")
        
        manage_dialog.destroy()
        root.destroy()
        
        if functions_found == len(logo_functions):
            print("✅ جميع دوال الشعار موجودة")
            return True
        else:
            print(f"❌ بعض دوال الشعار مفقودة ({functions_found}/{len(logo_functions)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال الشعار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_header_formatting():
    """اختبار تنسيق الترويسة مع مساحة الشعار"""
    try:
        print("\n🧪 اختبار تنسيق الترويسة مع مساحة الشعار...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "LOGO"
        account_name = "حساب اختبار الشعار"
        balance = 1000
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        ws = excel.workbook[sheet_name]
        
        print(f"✅ تم إنشاء الحساب: {sheet_name}")
        
        # التحقق من مساحة الشعار
        logo_cell = ws['A1']
        logo_value = logo_cell.value
        
        print(f"📋 محتوى مساحة الشعار (A1): {logo_value}")
        
        # التحقق من دمج الخلايا للشعار
        merged_ranges = ws.merged_cells.ranges
        logo_merged = False
        for merged_range in merged_ranges:
            if str(merged_range) == 'A1:D4':
                logo_merged = True
                print("✅ خلايا الشعار مدموجة بشكل صحيح (A1:D4)")
                break
        
        if not logo_merged:
            print("❌ خلايا الشعار غير مدموجة")
            return False
        
        # التحقق من الترويسة الرسمية
        header_checks = [
            ('E1', 'المملكة الأردنية الهاشمية'),
            ('E2', 'وزارة الصحة'),
            ('E3', f'حساب: {account_name} - {account_num}')
        ]
        
        headers_correct = 0
        for cell_ref, expected_value in header_checks:
            actual_value = ws[cell_ref].value
            if expected_value in str(actual_value):
                print(f"✅ ترويسة صحيحة ({cell_ref}): {actual_value}")
                headers_correct += 1
            else:
                print(f"❌ ترويسة خاطئة ({cell_ref}): {actual_value}")
        
        if headers_correct == len(header_checks):
            print("✅ تنسيق الترويسة مع مساحة الشعار صحيح")
            return True
        else:
            print(f"❌ تنسيق الترويسة ناقص ({headers_correct}/{len(header_checks)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تنسيق الترويسة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_formatting():
    """اختبار التنسيق المحسن للأعمدة والخلايا"""
    try:
        print("\n🧪 اختبار التنسيق المحسن للأعمدة والخلايا...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "FMT"
        account_name = "حساب اختبار التنسيق"
        balance = 1500.123
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        ws = excel.workbook[sheet_name]
        
        # إضافة مستند للاختبار
        excel.add_document(sheet_name, 250.456, "DOC001", "PAY001")
        
        print(f"✅ تم إنشاء الحساب وإضافة مستند")
        
        # التحقق من تنسيق العناوين
        header_cell = ws['A7']  # عنوان "المبلغ"
        header_font = header_cell.font
        header_fill = header_cell.fill
        
        print(f"📋 تنسيق العنوان:")
        print(f"   الخط: حجم={header_font.size}, غامق={header_font.bold}, لون={header_font.color}")
        print(f"   الخلفية: {header_fill.start_color}")
        
        # التحقق من تنسيق الرصيد الافتتاحي
        balance_cell = ws['A8']
        balance_font = balance_cell.font
        balance_fill = balance_cell.fill
        balance_format = balance_cell.number_format
        
        print(f"📋 تنسيق الرصيد الافتتاحي:")
        print(f"   القيمة: {balance_cell.value}")
        print(f"   التنسيق: {balance_format}")
        print(f"   الخط: حجم={balance_font.size}, غامق={balance_font.bold}, لون={balance_font.color}")
        print(f"   الخلفية: {balance_fill.start_color}")
        
        # التحقق من تنسيق المجموع
        total_cell = ws['A31']
        total_font = total_cell.font
        total_fill = total_cell.fill
        total_format = total_cell.number_format
        
        print(f"📋 تنسيق المجموع:")
        print(f"   الصيغة: {total_cell.value}")
        print(f"   التنسيق: {total_format}")
        print(f"   الخط: حجم={total_font.size}, غامق={total_font.bold}, لون={total_font.color}")
        print(f"   الخلفية: {total_fill.start_color}")
        
        # التحقق من التنسيق التلقائ<|im_start|>
        formatting_checks = [
            (header_font.bold == True, "العناوين غامقة"),
            (balance_font.bold == True, "الرصيد غامق"),
            (total_font.bold == True, "المجموع غامق"),
            (balance_format == '#,##0.000', "تنسيق الرصيد ثلاث خانات"),
            (total_format == '#,##0.000', "تنسيق المجموع ثلاث خانات"),
        ]
        
        checks_passed = 0
        for check_result, check_name in formatting_checks:
            if check_result:
                print(f"✅ {check_name}")
                checks_passed += 1
            else:
                print(f"❌ {check_name}")
        
        if checks_passed >= 4:  # معظم التنسيقات صحيحة
            print("✅ التنسيق المحسن يعمل بشكل صحيح")
            return True
        else:
            print(f"❌ التنسيق المحسن ناقص ({checks_passed}/{len(formatting_checks)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التنسيق المحسن: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_dialog_classes():
    """اختبار وجود نوافذ الحوار الجديدة"""
    try:
        print("\n🧪 اختبار وجود نوافذ الحوار الجديدة...")
        
        # فحص كود ملف إدارة الحسابات
        with open('manage_accounts.py', 'r', encoding='utf-8') as f:
            manage_content = f.read()
        
        # التحقق من وجود الكلاسات
        dialog_classes = [
            'class LogoConfirmDialog',      # نافذة تأكيد الشعار
            'class LogoProgressDialog',     # نافذة تقدم الشعار
            'def create_widgets',           # دالة إنشاء العناصر
            'def center_window',            # دالة توسيط النافذة
            'PIL import Image',             # استيراد PIL للصور
            'ImageTk.PhotoImage',           # تحويل الصور
        ]
        
        classes_found = 0
        for class_element in dialog_classes:
            if class_element in manage_content:
                print(f"✅ عنصر موجود: {class_element}")
                classes_found += 1
            else:
                print(f"❌ عنصر مفقود: {class_element}")
        
        if classes_found >= 4:  # معظم العناصر موجودة
            print("✅ نوافذ الحوار الجديدة موجودة")
            return True
        else:
            print(f"❌ بعض نوافذ الحوار مفقودة ({classes_found}/{len(dialog_classes)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نوافذ الحوار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار ميزة الشعار والتنسيق المحسن")
    print("=" * 60)
    print("الأهداف:")
    print("1. التحقق من وجود زر اختيار الشعار")
    print("2. التحقق من دوال الشعار")
    print("3. التحقق من تنسيق الترويسة مع مساحة الشعار")
    print("4. التحقق من التنسيق المحسن للأعمدة")
    print("5. التحقق من نوافذ الحوار الجديدة")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 5
    
    # اختبار زر الشعار
    if test_logo_button_exists():
        success_count += 1
    
    # اختبار دوال الشعار
    if test_logo_functions():
        success_count += 1
    
    # اختبار تنسيق الترويسة
    if test_header_formatting():
        success_count += 1
    
    # اختبار التنسيق المحسن
    if test_enhanced_formatting():
        success_count += 1
    
    # اختبار نوافذ الحوار
    if test_dialog_classes():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات الشعار والتنسيق!")
        print("\n✅ المميزات المختبرة:")
        print("   - زر اختيار الشعار في إدارة الحسابات")
        print("   - مساحة مخصصة للشعار (4 صفوف)")
        print("   - تنسيق محسن للعناوين (غامق وملون)")
        print("   - تنسيق محسن للرصيد (أغمق)")
        print("   - تنسيق تلقائ<|im_start|> لجميع الأعمدة")
        print("   - نوافذ حوار متقدمة للشعار")
        
        print("\n🔧 للاستخدام:")
        print("   1. شغل النظام: run_simple.bat")
        print("   2. اذهب إلى: الحسابات → إدارة الحسابات")
        print("   3. اضغط زر 'اختيار الشعار'")
        print("   4. اختر ملف صورة (PNG, JPG, etc.)")
        print("   5. أكد الاختيار - سيتم تطبيقه على جميع الحسابات")
        
        print("\n🎨 التنسيق الجديد:")
        print("   - مساحة الشعار: A1:D4 (4 صفوف)")
        print("   - العناوين: خلفية زرقاء، خط أبيض غامق")
        print("   - الرصيد الافتتاحي: خلفية خضراء غامقة")
        print("   - ترحيل الرصيد: خلفية حمراء غامقة")
        print("   - المجموع: خلفية زرقاء غامقة")
        print("   - الخلايا العادية: تلوين متناوب")
        
        return True
    else:
        print("❌ فشل في بعض اختبارات الشعار والتنسيق")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
