#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تنسيق صفحة الحساب المحدث
التحقق من أن الصيغ والمراجع تعمل بشكل صحيح
"""

import os
import sys

def test_account_page_format():
    """اختبار تنسيق صفحة الحساب"""
    try:
        print("🧪 اختبار تنسيق صفحة الحساب المحدث...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "55"
        account_name = "حساب اختبار التنسيق"
        balance = 1000
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        print(f"✅ تم إنشاء الحساب: {sheet_name}")
        
        # فحص تنسيق الصفحة
        ws = excel.workbook[sheet_name]
        
        # التحقق من الرصيد الافتتاحي في الصف 8
        opening_balance = ws['A8'].value
        print(f"📋 الرصيد الافتتاحي (A8): {opening_balance}")
        
        # التحقق من صيغة المجموع في الصف 32
        sum_formula = ws['A32'].value
        print(f"📋 صيغة المجموع (A32): {sum_formula}")
        
        # التحقق من نص "المجموع" في الصف 31
        total_text = ws['A31'].value
        print(f"📋 نص المجموع (A31): {total_text}")
        
        # إضافة بعض المستندات للاختبار
        excel.add_document(sheet_name, 100, "DOC001", "PAY001")
        excel.add_document(sheet_name, 200, "DOC002", "PAY002")
        excel.add_document(sheet_name, 300, "DOC003", "PAY003")
        
        print("✅ تم إضافة مستندات تجريبية")
        
        # التحقق من الصيغ في الأقسام الأخرى
        sections_correct = True
        for i in range(1, 6):  # الأقسام من D إلى P
            col = chr(68 + (i-1) * 3)  # D, G, J, M, P
            prev_col = chr(65 + (i-1) * 3)  # A, D, G, J, M
            
            # التحقق من صيغة "ما قبله"
            carry_formula = ws[f'{col}8'].value
            expected_formula = f"={prev_col}32"
            
            if carry_formula == expected_formula:
                print(f"✅ صيغة ترحيل الرصيد صحيحة في {col}8: {carry_formula}")
            else:
                print(f"❌ صيغة ترحيل الرصيد خاطئة في {col}8: {carry_formula} (متوقع: {expected_formula})")
                sections_correct = False
        
        return sections_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تنسيق الصفحة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_report_references():
    """اختبار مراجع التقارير"""
    try:
        print("\n🧪 اختبار مراجع التقارير...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "TEST"
        account_name = "حساب اختبار التقارير"
        balance = 2000
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        
        # إضافة مستندات
        excel.add_document(sheet_name, 500, "DOC001", "PAY001")
        excel.add_document(sheet_name, 750, "DOC002", "PAY002")
        
        # إنشاء التقرير الإجمالي
        excel.create_summary_report()
        
        print("✅ تم إنشاء التقرير الإجمالي")
        
        # التحقق من وجود التقرير
        if 'التقرير الإجمالي' in excel.workbook.sheetnames:
            print("✅ التقرير الإجمالي موجود")
            
            # فحص بيانات الحساب في التقرير
            ws_report = excel.workbook['التقرير الإجمالي']
            
            # البحث عن الحساب في التقرير
            found_account = False
            for row in range(7, 50):  # فحص الصفوف المحتملة
                account_cell = ws_report.cell(row=row, column=1).value
                if account_cell == account_num:
                    found_account = True
                    opening_balance = ws_report.cell(row=row, column=3).value
                    documents_total = ws_report.cell(row=row, column=4).value
                    final_balance = ws_report.cell(row=row, column=5).value
                    
                    print(f"✅ تم العثور على الحساب في التقرير:")
                    print(f"   الرصيد الافتتاحي: {opening_balance}")
                    print(f"   مجموع المستندات: {documents_total}")
                    print(f"   الرصيد النهائي: {final_balance}")
                    break
            
            if not found_account:
                print("❌ لم يتم العثور على الحساب في التقرير")
                return False
            
            return True
        else:
            print("❌ التقرير الإجمالي غير موجود")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التقارير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_document_ranges():
    """اختبار نطاقات المستندات"""
    try:
        print("\n🧪 اختبار نطاقات المستندات...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "RANGE"
        account_name = "حساب اختبار النطاقات"
        balance = 1500
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        if not result:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        ws = excel.workbook[sheet_name]
        
        # إضافة مستندات في نطاقات مختلفة
        test_documents = [
            (100, "DOC001", "PAY001"),
            (200, "DOC002", "PAY002"),
            (300, "DOC003", "PAY003"),
            (400, "DOC004", "PAY004"),
            (500, "DOC005", "PAY005")
        ]
        
        for amount, doc_num, pay_num in test_documents:
            excel.add_document(sheet_name, amount, doc_num, pay_num)
        
        print(f"✅ تم إضافة {len(test_documents)} مستند")
        
        # التحقق من النطاقات
        # فحص الصفوف من 9 إلى 30 (نطاق المستندات)
        documents_found = 0
        for row in range(9, 31):
            amount = ws.cell(row=row, column=1).value
            if amount and isinstance(amount, (int, float)) and amount > 0:
                documents_found += 1
                print(f"   مستند في الصف {row}: {amount}")
        
        print(f"✅ تم العثور على {documents_found} مستند في النطاق الصحيح")
        
        # التحقق من صيغة المجموع
        sum_formula = ws['A32'].value
        if isinstance(sum_formula, str) and "SUM(A9:A30)" in sum_formula:
            print("✅ صيغة المجموع صحيحة: تشمل النطاق من A9 إلى A30")
            return True
        else:
            print(f"❌ صيغة المجموع خاطئة: {sum_formula}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النطاقات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار تنسيق صفحة الحساب المحدث")
    print("=" * 60)
    print("الأهداف:")
    print("1. التحقق من تنسيق الصفحة الجديد")
    print("2. التحقق من صحة الصيغ والمراجع")
    print("3. التحقق من عمل التقارير")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 3
    
    # اختبار تنسيق الصفحة
    if test_account_page_format():
        success_count += 1
    
    # اختبار مراجع التقارير
    if test_report_references():
        success_count += 1
    
    # اختبار نطاقات المستندات
    if test_document_ranges():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات تنسيق الصفحة!")
        print("\n✅ التنسيق الجديد:")
        print("   - الرصيد الافتتاحي في الصف 8")
        print("   - المستندات من الصف 9 إلى 30")
        print("   - نص 'المجموع' في الصف 31")
        print("   - صيغة المجموع في الصف 32")
        print("   - صيغ ترحيل الرصيد تشير إلى الصف 32")
        
        print("\n🔧 للاستخدام:")
        print("   1. شغل النظام: run_simple.bat")
        print("   2. أضف حساب جديد - سيكون بالتنسيق الجديد")
        print("   3. أضف مستندات واختبر الصيغ")
        
        return True
    else:
        print("❌ فشل في بعض اختبارات تنسيق الصفحة")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
