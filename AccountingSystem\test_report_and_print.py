#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التقرير الإجمالي وميزة الطباعة
"""

import os
import sys
import tkinter as tk

def test_summary_report_creation():
    """اختبار إنشاء التقرير الإجمالي"""
    try:
        print("🧪 اختبار إنشاء التقرير الإجمالي...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حسابات تجريبية
        test_accounts = [
            ("001", "حساب أول", 1000),
            ("002", "حساب ثاني", 2000),
            ("003", "حساب ثالث", 1500)
        ]
        
        for account_num, account_name, balance in test_accounts:
            result = excel.create_account_sheet(account_num, account_name, balance)
            if result:
                print(f"✅ تم إنشاء الحساب: {account_num}-{account_name}")
                
                # إضافة بعض المستندات
                sheet_name = f"{account_num}-{account_name}"
                excel.add_document(sheet_name, 500, f"DOC{account_num}1", f"PAY{account_num}1")
                excel.add_document(sheet_name, 300, f"DOC{account_num}2", f"PAY{account_num}2")
            else:
                print(f"❌ فشل في إنشاء الحساب: {account_num}")
                return False
        
        # إنشاء التقرير الإجمالي
        print("📊 إنشاء التقرير الإجمالي...")
        result = excel.create_summary_report()
        
        if result:
            print("✅ تم إنشاء التقرير الإجمالي بنجاح")
            
            # التحقق من وجود التقرير
            if 'التقرير الإجمالي' in excel.workbook.sheetnames:
                print("✅ ورقة التقرير الإجمالي موجودة")
                
                # فحص محتوى التقرير
                ws = excel.workbook['التقرير الإجمالي']
                
                # التحقق من الترويسة
                header_title = ws['A1'].value
                ministry = ws['A2'].value
                report_title = ws['A3'].value
                
                print(f"📋 عنوان الترويسة: {header_title}")
                print(f"📋 الوزارة: {ministry}")
                print(f"📋 عنوان التقرير: {report_title}")
                
                # التحقق من عناوين الأعمدة
                headers = []
                for col in range(1, 7):
                    header = ws.cell(row=6, column=col).value
                    headers.append(header)
                
                print(f"📋 عناوين الأعمدة: {headers}")
                
                # عد الحسابات في التقرير
                accounts_count = 0
                for row in range(7, ws.max_row + 1):
                    if ws.cell(row=row, column=1).value:
                        accounts_count += 1
                        account_data = []
                        for col in range(1, 7):
                            value = ws.cell(row=row, column=col).value
                            account_data.append(value)
                        print(f"   حساب {accounts_count}: {account_data}")
                
                print(f"✅ تم العثور على {accounts_count} حساب في التقرير")
                
                if accounts_count == len(test_accounts):
                    print("✅ عدد الحسابات في التقرير صحيح")
                    return True
                else:
                    print(f"❌ عدد الحسابات خاطئ: {accounts_count} (متوقع: {len(test_accounts)})")
                    return False
            else:
                print("❌ ورقة التقرير الإجمالي غير موجودة")
                return False
        else:
            print("❌ فشل في إنشاء التقرير الإجمالي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التقرير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_print_functions():
    """اختبار وجود دوال الطباعة"""
    try:
        print("\n🧪 اختبار وجود دوال الطباعة...")
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        from app import AccountingApp
        app = AccountingApp(root)
        
        # التحقق من وجود دوال الطباعة
        functions_to_check = [
            'print_summary_report',
            'export_summary_report', 
            'generate_report_html'
        ]
        
        functions_found = 0
        for func_name in functions_to_check:
            if hasattr(app, func_name):
                print(f"✅ دالة {func_name} موجودة")
                functions_found += 1
            else:
                print(f"❌ دالة {func_name} غير موجودة")
        
        root.destroy()
        
        if functions_found == len(functions_to_check):
            print("✅ جميع دوال الطباعة موجودة")
            return True
        else:
            print(f"❌ بعض دوال الطباعة مفقودة ({functions_found}/{len(functions_to_check)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال الطباعة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_html_generation():
    """اختبار إنشاء HTML للطباعة"""
    try:
        print("\n🧪 اختبار إنشاء HTML للطباعة...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        excel.create_account_sheet("HTML", "حساب اختبار HTML", 1000)
        excel.add_document("HTML-حساب اختبار HTML", 500, "DOC001", "PAY001")
        
        # إنشاء التقرير
        result = excel.create_summary_report()
        if not result:
            print("❌ فشل في إنشاء التقرير")
            return False
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        from app import AccountingApp
        app = AccountingApp(root)
        
        # اختبار إنشاء HTML
        ws = excel.workbook['التقرير الإجمالي']
        html_content = app.generate_report_html(ws)
        
        # التحقق من محتوى HTML
        html_checks = [
            'DOCTYPE html',
            'dir="rtl"',
            'المملكة الأردنية الهاشمية',
            'وزارة الصحة',
            'التقرير الإجمالي',
            'report-table',
            'window.print()'
        ]
        
        checks_passed = 0
        for check in html_checks:
            if check in html_content:
                print(f"✅ عنصر HTML موجود: {check}")
                checks_passed += 1
            else:
                print(f"❌ عنصر HTML مفقود: {check}")
        
        root.destroy()
        
        if checks_passed == len(html_checks):
            print("✅ محتوى HTML صحيح ومكتمل")
            return True
        else:
            print(f"❌ محتوى HTML ناقص ({checks_passed}/{len(html_checks)})")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار HTML: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_cmd_closure():
    """اختبار إعداد إغلاق CMD"""
    try:
        print("\n🧪 اختبار إعداد إغلاق CMD...")
        
        # فحص ملف run_simple.bat
        bat_file = "run_simple.bat"
        if os.path.exists(bat_file):
            with open(bat_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود أمر start
            if 'start ""' in content:
                print("✅ أمر start موجود - سيتم تشغيل البرنامج في الخلفية")
            else:
                print("❌ أمر start غير موجود")
                return False
            
            # التحقق من وجود أمر exit
            if content.strip().endswith('exit'):
                print("✅ أمر exit موجود - سيتم إغلاق CMD تلقائياً")
            else:
                print("❌ أمر exit غير موجود")
                return False
            
            # التحقق من عدم وجود pause
            if 'pause' not in content or 'REM pause' in content:
                print("✅ أمر pause تم إزالته أو تعطيله")
            else:
                print("❌ أمر pause لا يزال موجود")
                return False
            
            print("✅ إعداد إغلاق CMD صحيح")
            return True
        else:
            print(f"❌ ملف {bat_file} غير موجود")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إغلاق CMD: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_report_data_accuracy():
    """اختبار دقة بيانات التقرير"""
    try:
        print("\n🧪 اختبار دقة بيانات التقرير...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي بقيم محددة
        account_num = "ACC"
        account_name = "حساب اختبار الدقة"
        opening_balance = 1000
        
        excel.create_account_sheet(account_num, account_name, opening_balance)
        sheet_name = f"{account_num}-{account_name}"
        
        # إضافة مستندات بقيم محددة
        documents = [
            (200, "DOC001", "PAY001"),
            (300, "DOC002", "PAY002"),
            (150, "DOC003", "PAY003")
        ]
        
        total_documents = 0
        for amount, doc_num, pay_num in documents:
            excel.add_document(sheet_name, amount, doc_num, pay_num)
            total_documents += amount
        
        print(f"📋 الرصيد الافتتاحي: {opening_balance}")
        print(f"📋 مجموع المستندات: {total_documents}")
        print(f"📋 الرصيد المتوقع: {opening_balance + total_documents}")
        
        # إنشاء التقرير
        result = excel.create_summary_report()
        if not result:
            print("❌ فشل في إنشاء التقرير")
            return False
        
        # فحص بيانات التقرير
        ws = excel.workbook['التقرير الإجمالي']
        
        # البحث عن الحساب في التقرير
        found_account = False
        for row in range(7, ws.max_row + 1):
            if ws.cell(row=row, column=1).value == account_num:
                found_account = True
                
                report_opening = ws.cell(row=row, column=3).value
                report_documents = ws.cell(row=row, column=4).value
                report_final = ws.cell(row=row, column=5).value
                report_count = ws.cell(row=row, column=6).value
                
                print(f"📋 التقرير - الرصيد الافتتاحي: {report_opening}")
                print(f"📋 التقرير - مجموع المستندات: {report_documents}")
                print(f"📋 التقرير - الرصيد النهائي: {report_final}")
                print(f"📋 التقرير - عدد المستندات: {report_count}")
                
                # التحقق من الدقة
                accuracy_checks = [
                    (report_opening == opening_balance, "الرصيد الافتتاحي"),
                    (report_count == len(documents), "عدد المستندات"),
                    (isinstance(report_final, (int, float)), "الرصيد النهائي رقمي")
                ]
                
                checks_passed = 0
                for check_result, check_name in accuracy_checks:
                    if check_result:
                        print(f"✅ {check_name} صحيح")
                        checks_passed += 1
                    else:
                        print(f"❌ {check_name} خاطئ")
                
                if checks_passed == len(accuracy_checks):
                    print("✅ بيانات التقرير دقيقة")
                    return True
                else:
                    print(f"❌ بيانات التقرير غير دقيقة ({checks_passed}/{len(accuracy_checks)})")
                    return False
                
                break
        
        if not found_account:
            print("❌ لم يتم العثور على الحساب في التقرير")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دقة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار التقرير الإجمالي والطباعة")
    print("=" * 60)
    print("الأهداف:")
    print("1. التحقق من إنشاء التقرير الإجمالي")
    print("2. التحقق من دوال الطباعة والتصدير")
    print("3. التحقق من إنشاء HTML للطباعة")
    print("4. التحقق من إغلاق CMD تلقائياً")
    print("5. التحقق من دقة بيانات التقرير")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 5
    
    # اختبار إنشاء التقرير
    if test_summary_report_creation():
        success_count += 1
    
    # اختبار دوال الطباعة
    if test_print_functions():
        success_count += 1
    
    # اختبار إنشاء HTML
    if test_html_generation():
        success_count += 1
    
    # اختبار إغلاق CMD
    if test_cmd_closure():
        success_count += 1
    
    # اختبار دقة البيانات
    if test_report_data_accuracy():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات التقرير والطباعة!")
        print("\n✅ المميزات المختبرة:")
        print("   - إنشاء التقرير الإجمالي بنجاح")
        print("   - دوال الطباعة والتصدير موجودة")
        print("   - إنشاء HTML للطباعة يعمل")
        print("   - إغلاق CMD تلقائياً")
        print("   - دقة بيانات التقرير")
        
        print("\n🔧 للاستخدام:")
        print("   1. شغل النظام: run_simple.bat")
        print("   2. أضف حسابات ومستندات")
        print("   3. اذهب إلى التقارير → التقرير الإجمالي")
        print("   4. استخدم أزرار الطباعة والتصدير")
        
        return True
    else:
        print("❌ فشل في بعض اختبارات التقرير والطباعة")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
