#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التقرير الإجمالي
"""

import os
import sys

def test_summary_report():
    """اختبار إنشاء التقرير الإجمالي"""
    try:
        print("🧪 اختبار التقرير الإجمالي...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حسابات تجريبية
        test_accounts = [
            ("001", "حساب اختبار 1", 5000),
            ("002", "حساب اختبار 2", 3000),
            ("003", "حساب اختبار 3", 0)
        ]
        
        print(f"📝 إنشاء {len(test_accounts)} حسابات تجريبية...")
        for account_num, account_name, balance in test_accounts:
            result = excel.create_account_sheet(account_num, account_name, balance)
            if result:
                print(f"✅ تم إنشاء الحساب: {account_num}-{account_name}")
            else:
                print(f"❌ فشل في إنشاء الحساب: {account_num}-{account_name}")
        
        # إضافة مستندات
        print(f"📄 إضافة مستندات...")
        excel.add_document("001-حساب اختبار 1", 1000, "DOC001", "PAY001")
        excel.add_document("001-حساب اختبار 1", 2000, "DOC002", "PAY002")
        excel.add_document("002-حساب اختبار 2", 500, "DOC003", "PAY003")
        
        # التحقق من وجود التقرير الإجمالي
        print(f"📊 التحقق من التقرير الإجمالي...")
        if 'التقرير الإجمالي' in excel.workbook.sheetnames:
            print(f"✅ التقرير الإجمالي موجود")
            
            # فحص محتوى التقرير
            ws_report = excel.workbook['التقرير الإجمالي']
            
            print(f"📋 محتوى التقرير:")
            print(f"   العنوان الرئيسي: {ws_report['A1'].value}")
            print(f"   وزارة الصحة: {ws_report['A2'].value}")
            print(f"   عنوان التقرير: {ws_report['A3'].value}")
            
            # فحص العناوين
            headers = []
            for col in range(1, 7):
                header = ws_report.cell(row=6, column=col).value
                headers.append(header)
            print(f"   العناوين: {headers}")
            
            # فحص البيانات
            accounts_found = 0
            for row in range(7, 15):
                account_name = ws_report.cell(row=row, column=2).value
                if account_name:
                    accounts_found += 1
                    opening = ws_report.cell(row=row, column=3).value
                    documents = ws_report.cell(row=row, column=4).value
                    final = ws_report.cell(row=row, column=5).value
                    count = ws_report.cell(row=row, column=6).value
                    print(f"   {account_name}: افتتاحي={opening}, مستندات={documents}, نهائي={final}, عدد={count}")
            
            print(f"📊 عدد الحسابات في التقرير: {accounts_found}")
            
            if accounts_found >= len(test_accounts):
                print(f"✅ التقرير يحتوي على جميع الحسابات")
                return True
            else:
                print(f"❌ التقرير لا يحتوي على جميع الحسابات")
                return False
        else:
            print(f"❌ التقرير الإجمالي غير موجود")
            
            # طباعة أسماء الأوراق الموجودة
            print(f"📋 الأوراق الموجودة:")
            for sheet_name in excel.workbook.sheetnames:
                print(f"   - {sheet_name}")
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_summary_report():
    """اختبار إنشاء التقرير الإجمالي يدوياً"""
    try:
        print("\n🧪 اختبار إنشاء التقرير الإجمالي يدوياً...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب واحد
        result = excel.create_account_sheet("TEST", "حساب اختبار", 1000)
        if not result:
            print("❌ فشل في إنشاء الحساب")
            return False
        
        print("✅ تم إنشاء الحساب")
        
        # إنشاء التقرير يدوياً
        print("📊 إنشاء التقرير يدوياً...")
        result = excel.create_summary_report()
        
        if result:
            print("✅ تم إنشاء التقرير بنجاح")
            
            # التحقق من وجوده
            if 'التقرير الإجمالي' in excel.workbook.sheetnames:
                print("✅ التقرير موجود في الملف")
                return True
            else:
                print("❌ التقرير غير موجود في الملف")
                return False
        else:
            print("❌ فشل في إنشاء التقرير")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار اليدوي: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_report_functions():
    """اختبار دوال التقرير منفردة"""
    try:
        print("\n🧪 اختبار دوال التقرير منفردة...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب
        excel.create_account_sheet("FUNC", "اختبار دوال", 500)
        
        # إنشاء ورقة التقرير
        report_sheet_name = 'التقرير الإجمالي'
        if report_sheet_name in excel.workbook.sheetnames:
            excel.workbook.remove(excel.workbook[report_sheet_name])
        
        ws = excel.workbook.create_sheet(report_sheet_name, 0)
        ws.sheet_properties.rightToLeft = True
        print("✅ تم إنشاء ورقة التقرير")
        
        # اختبار دالة الترويسة
        try:
            excel._setup_report_header(ws)
            print("✅ دالة الترويسة تعمل")
        except Exception as e:
            print(f"❌ خطأ في دالة الترويسة: {str(e)}")
            return False
        
        # اختبار دالة العناوين
        try:
            excel._setup_report_columns(ws)
            print("✅ دالة العناوين تعمل")
        except Exception as e:
            print(f"❌ خطأ في دالة العناوين: {str(e)}")
            return False
        
        # اختبار دالة البيانات
        try:
            excel._add_accounts_data(ws)
            print("✅ دالة البيانات تعمل")
        except Exception as e:
            print(f"❌ خطأ في دالة البيانات: {str(e)}")
            return False
        
        # اختبار دالة المجاميع
        try:
            excel._add_report_totals(ws)
            print("✅ دالة المجاميع تعمل")
        except Exception as e:
            print(f"❌ خطأ في دالة المجاميع: {str(e)}")
            return False
        
        # اختبار دالة التنسيق
        try:
            excel._format_report(ws)
            print("✅ دالة التنسيق تعمل")
        except Exception as e:
            print(f"❌ خطأ في دالة التنسيق: {str(e)}")
            return False
        
        print("✅ جميع دوال التقرير تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار التقرير الإجمالي")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    success_count = 0
    total_tests = 3
    
    # اختبار التقرير التلقائي
    if test_summary_report():
        success_count += 1
    
    # اختبار التقرير اليدوي
    if test_manual_summary_report():
        success_count += 1
    
    # اختبار الدوال منفردة
    if test_report_functions():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 نجحت جميع اختبارات التقرير الإجمالي!")
        return True
    else:
        print("❌ فشل في بعض اختبارات التقرير الإجمالي")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
