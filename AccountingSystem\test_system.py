#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للنظام المحاسبي
"""

import sys
import os

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔍 اختبار استيراد المكتبات...")
    
    try:
        import tkinter as tk
        print("✅ tkinter - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ tkinter - فشل الاستيراد: {e}")
        return False
    
    try:
        import openpyxl
        print("✅ openpyxl - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ openpyxl - فشل الاستيراد: {e}")
        return False
    
    try:
        from excel_manager import ExcelManager
        print("✅ ExcelManager - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ ExcelManager - فشل الاستيراد: {e}")
        return False
    
    try:
        from app import AccountingApp
        print("✅ AccountingApp - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ AccountingApp - فشل الاستيراد: {e}")
        return False
    
    return True

def test_excel_manager():
    """اختبار ExcelManager"""
    print("\n📊 اختبار ExcelManager...")
    
    try:
        from excel_manager import ExcelManager
        excel = ExcelManager()
        print("✅ تم إنشاء ExcelManager بنجاح")
        
        # اختبار إنشاء حساب
        result = excel.create_account_sheet("001", "حساب تجريبي", 1000)
        if result:
            print("✅ تم إنشاء حساب تجريبي بنجاح")
        else:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        # اختبار إضافة مستند
        result = excel.add_document("001-حساب تجريبي", 500, "DOC001", "PAY001")
        if result:
            print("✅ تم إضافة مستند تجريبي بنجاح")
        else:
            print("❌ فشل في إضافة المستند التجريبي")
            return False
        
        # اختبار الحفظ
        if excel.save_workbook():
            print("✅ تم حفظ الملف بنجاح")
        else:
            print("❌ فشل في حفظ الملف")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ExcelManager: {e}")
        return False

def test_gui():
    """اختبار الواجهة الرسومية"""
    print("\n🖥️ اختبار الواجهة الرسومية...")
    
    try:
        import tkinter as tk
        from app import AccountingApp
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار النظام")
        root.geometry("300x200")
        
        # إنشاء التطبيق
        app = AccountingApp(root)
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # إغلاق النافذة فوراً
        root.after(1000, root.destroy)  # إغلاق بعد ثانية واحدة
        root.mainloop()
        
        print("✅ تم اختبار الواجهة الرسومية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة الرسومية: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار النظام المحاسبي")
    print("=" * 50)
    
    # اختبار الاستيراد
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيراد")
        return False
    
    # اختبار ExcelManager
    if not test_excel_manager():
        print("\n❌ فشل في اختبار ExcelManager")
        return False
    
    # اختبار الواجهة الرسومية
    if not test_gui():
        print("\n❌ فشل في اختبار الواجهة الرسومية")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 تم اجتياز جميع الاختبارات بنجاح!")
    print("✅ النظام جاهز للاستخدام")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ فشل في الاختبار")
        sys.exit(1)
    else:
        print("\n✅ نجح الاختبار")
        sys.exit(0)
