#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق الشامل من تنسيق صفحة الحساب
"""

import os
import sys

def verify_account_structure():
    """التحقق من هيكل صفحة الحساب"""
    try:
        print("🔍 التحقق من هيكل صفحة الحساب...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # إنشاء حساب تجريبي
        account_num = "VERIFY"
        account_name = "حساب التحقق من التنسيق"
        balance = 2500.789
        
        print(f"📝 إنشاء حساب: {account_num} - {account_name}")
        print(f"💰 الرصيد: {balance}")
        
        result = excel.create_account_sheet(account_num, account_name, balance)
        
        if not result:
            print("❌ فشل في إنشاء الحساب")
            return False
        
        sheet_name = f"{account_num}-{account_name}"
        ws = excel.workbook[sheet_name]
        
        print("✅ تم إنشاء الحساب بنجاح")
        
        # فحص الهيكل العام
        print("\n📋 فحص الهيكل العام:")
        
        # 1. مساحة الشعار (A1:D4)
        logo_merged = False
        for merged_range in ws.merged_cells.ranges:
            if str(merged_range) == 'A1:D4':
                logo_merged = True
                break
        
        logo_content = ws['A1'].value
        print(f"   مساحة الشعار (A1:D4): {'✅ مدموجة' if logo_merged else '❌ غير مدموجة'}")
        print(f"   محتوى الشعار: {logo_content}")
        
        # 2. الترويسة الرسمية (E1:R4)
        headers = [
            ('E1', 'المملكة الأردنية الهاشمية'),
            ('E2', 'وزارة الصحة'),
            ('E3', f'حساب: {account_name} - {account_num}'),
            ('E4', 'تاريخ الإنشاء')
        ]
        
        headers_ok = 0
        for cell_ref, expected_text in headers:
            actual_value = ws[cell_ref].value
            if expected_text in str(actual_value):
                print(f"   ترويسة {cell_ref}: ✅ {actual_value}")
                headers_ok += 1
            else:
                print(f"   ترويسة {cell_ref}: ❌ {actual_value}")
        
        # 3. عنوان الأقسام (A5)
        section_title = ws['A5'].value
        print(f"   عنوان الأقسام (A5): {section_title}")
        
        # 4. عناوين الأعمدة (صف 6)
        column_headers = [
            ('A6', 'المبلغ'),
            ('B6', 'مستند الصرف'),
            ('C6', 'رقم التأدية')
        ]
        
        columns_ok = 0
        for cell_ref, expected_text in column_headers:
            actual_value = ws[cell_ref].value
            if actual_value == expected_text:
                print(f"   عنوان العمود {cell_ref}: ✅ {actual_value}")
                columns_ok += 1
            else:
                print(f"   عنوان العمود {cell_ref}: ❌ {actual_value} (متوقع: {expected_text})")
        
        # 5. الرصيد الافتتاحي (صف 8)
        opening_balance = ws['A8'].value
        opening_desc = ws['B8'].value
        print(f"   الرصيد الافتتاحي (A8): {opening_balance}")
        print(f"   وصف الرصيد (B8): {opening_desc}")
        
        # 6. صيغة المجموع (صف 31)
        total_formula = ws['A31'].value
        total_desc = ws['B31'].value
        print(f"   صيغة المجموع (A31): {total_formula}")
        print(f"   وصف المجموع (B31): {total_desc}")
        
        # تقييم الهيكل
        structure_score = 0
        if logo_merged: structure_score += 1
        if headers_ok >= 3: structure_score += 1
        if section_title: structure_score += 1
        if columns_ok >= 3: structure_score += 1
        if opening_balance == balance: structure_score += 1
        if isinstance(total_formula, str) and "SUM" in total_formula: structure_score += 1
        
        print(f"\n📊 تقييم الهيكل: {structure_score}/6")
        
        return structure_score >= 5
        
    except Exception as e:
        print(f"❌ خطأ في فحص الهيكل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_formatting():
    """التحقق من التنسيق والألوان"""
    try:
        print("\n🎨 التحقق من التنسيق والألوان...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        # استخدام الحساب الموجود
        sheet_name = "VERIFY-حساب التحقق من التنسيق"
        
        if sheet_name not in excel.workbook.sheetnames:
            print("❌ الحساب غير موجود")
            return False
        
        ws = excel.workbook[sheet_name]
        
        # فحص تنسيق العناوين
        print("   فحص تنسيق عناوين الأعمدة:")
        header_cell = ws['A6']
        header_font = header_cell.font
        header_fill = header_cell.fill
        
        print(f"     الخط: حجم={header_font.size}, غامق={header_font.bold}")
        print(f"     لون الخط: {header_font.color}")
        print(f"     لون الخلفية: {header_fill.start_color}")
        
        # فحص تنسيق الرصيد الافتتاحي
        print("   فحص تنسيق الرصيد الافتتاحي:")
        balance_cell = ws['A8']
        balance_font = balance_cell.font
        balance_fill = balance_cell.fill
        balance_format = balance_cell.number_format
        
        print(f"     القيمة: {balance_cell.value}")
        print(f"     تنسيق الرقم: {balance_format}")
        print(f"     الخط: حجم={balance_font.size}, غامق={balance_font.bold}")
        print(f"     لون الخط: {balance_font.color}")
        print(f"     لون الخلفية: {balance_fill.start_color}")
        
        # فحص تنسيق المجموع
        print("   فحص تنسيق المجموع:")
        total_cell = ws['A31']
        total_font = total_cell.font
        total_fill = total_cell.fill
        total_format = total_cell.number_format
        
        print(f"     الصيغة: {total_cell.value}")
        print(f"     تنسيق الرقم: {total_format}")
        print(f"     الخط: حجم={total_font.size}, غامق={total_font.bold}")
        print(f"     لون الخط: {total_font.color}")
        print(f"     لون الخلفية: {total_fill.start_color}")
        
        # فحص عرض الأعمدة
        print("   فحص عرض الأعمدة:")
        column_widths = ['A', 'B', 'C', 'D', 'E', 'F']
        for col in column_widths:
            width = ws.column_dimensions[col].width
            print(f"     العمود {col}: {width}")
        
        # تقييم التنسيق
        formatting_checks = [
            (header_font.bold == True, "عناوين الأعمدة غامقة"),
            (balance_font.bold == True, "الرصيد الافتتاحي غامق"),
            (total_font.bold == True, "المجموع غامق"),
            (balance_format == '#,##0.000', "تنسيق الرصيد ثلاث خانات"),
            (total_format == '#,##0.000', "تنسيق المجموع ثلاث خانات"),
            (ws.column_dimensions['A'].width >= 15, "عرض الأعمدة مناسب")
        ]
        
        formatting_score = 0
        for check_result, check_name in formatting_checks:
            if check_result:
                print(f"   ✅ {check_name}")
                formatting_score += 1
            else:
                print(f"   ❌ {check_name}")
        
        print(f"\n📊 تقييم التنسيق: {formatting_score}/{len(formatting_checks)}")
        
        return formatting_score >= 4
        
    except Exception as e:
        print(f"❌ خطأ في فحص التنسيق: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_document_addition():
    """التحقق من إضافة المستندات"""
    try:
        print("\n📝 التحقق من إضافة المستندات...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        sheet_name = "VERIFY-حساب التحقق من التنسيق"
        
        if sheet_name not in excel.workbook.sheetnames:
            print("❌ الحساب غير موجود")
            return False
        
        # إضافة عدة مستندات للاختبار
        test_documents = [
            (150.123, "DOC001", "PAY001"),
            (275.456, "DOC002", "PAY002"),
            (89.789, "DOC003", "PAY003"),
            (420.555, "DOC004", "PAY004")
        ]
        
        print(f"   إضافة {len(test_documents)} مستند...")
        
        documents_added = 0
        for amount, doc_num, pay_num in test_documents:
            result = excel.add_document(sheet_name, amount, doc_num, pay_num)
            if result:
                documents_added += 1
                print(f"   ✅ تم إضافة: {amount} | {doc_num} | {pay_num}")
            else:
                print(f"   ❌ فشل في إضافة: {amount} | {doc_num} | {pay_num}")
        
        print(f"   تم إضافة {documents_added}/{len(test_documents)} مستند")
        
        # فحص المستندات في الورقة
        ws = excel.workbook[sheet_name]
        
        print("   فحص المستندات في الورقة:")
        found_documents = 0
        for row in range(9, 31):  # نطاق المستندات
            amount = ws.cell(row=row, column=1).value
            doc_num = ws.cell(row=row, column=2).value
            pay_num = ws.cell(row=row, column=3).value
            
            if amount and doc_num and pay_num:
                found_documents += 1
                print(f"     الصف {row}: {amount} | {doc_num} | {pay_num}")
        
        print(f"   تم العثور على {found_documents} مستند في الورقة")
        
        return documents_added >= 3 and found_documents >= 3
        
    except Exception as e:
        print(f"❌ خطأ في فحص إضافة المستندات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_formulas():
    """التحقق من الصيغ والحسابات"""
    try:
        print("\n🧮 التحقق من الصيغ والحسابات...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        sheet_name = "VERIFY-حساب التحقق من التنسيق"
        
        if sheet_name not in excel.workbook.sheetnames:
            print("❌ الحساب غير موجود")
            return False
        
        ws = excel.workbook[sheet_name]
        
        # فحص صيغة المجموع في القسم الأول
        total_formula = ws['A31'].value
        print(f"   صيغة المجموع (A31): {total_formula}")
        
        # التحقق من أن الصيغة صحيحة
        expected_formula = "=SUM(A8:A30)"
        if isinstance(total_formula, str) and expected_formula in total_formula:
            print("   ✅ صيغة المجموع صحيحة")
        else:
            print(f"   ❌ صيغة المجموع خاطئة (متوقع: {expected_formula})")
            return False
        
        # فحص صيغة ترحيل الرصيد في القسم الثاني
        carry_formula = ws['D8'].value
        carry_text = ws['E8'].value
        print(f"   صيغة ترحيل الرصيد (D8): {carry_formula}")
        print(f"   نص الترحيل (E8): {carry_text}")
        
        # التحقق من صيغة الترحيل
        expected_carry = "=A31"
        if carry_formula == expected_carry and carry_text == "ما قبله":
            print("   ✅ صيغة ترحيل الرصيد صحيحة")
        else:
            print(f"   ❌ صيغة ترحيل الرصيد خاطئة (متوقع: {expected_carry})")
            return False
        
        # فحص صيغة المجموع في القسم الثاني
        second_total = ws['D31'].value
        print(f"   صيغة المجموع القسم الثاني (D31): {second_total}")
        
        expected_second = "=SUM(D8:D30)"
        if isinstance(second_total, str) and expected_second in second_total:
            print("   ✅ صيغة المجموع القسم الثاني صحيحة")
        else:
            print(f"   ❌ صيغة المجموع القسم الثاني خاطئة (متوقع: {expected_second})")
            return False
        
        print("   ✅ جميع الصيغ صحيحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الصيغ: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_logo_space():
    """التحقق من مساحة الشعار"""
    try:
        print("\n🖼️ التحقق من مساحة الشعار...")
        
        from excel_manager import ExcelManager
        excel = ExcelManager()
        
        sheet_name = "VERIFY-حساب التحقق من التنسيق"
        
        if sheet_name not in excel.workbook.sheetnames:
            print("❌ الحساب غير موجود")
            return False
        
        ws = excel.workbook[sheet_name]
        
        # فحص دمج الخلايا للشعار
        logo_merged = False
        for merged_range in ws.merged_cells.ranges:
            if str(merged_range) == 'A1:D4':
                logo_merged = True
                print("   ✅ خلايا الشعار مدموجة (A1:D4)")
                break
        
        if not logo_merged:
            print("   ❌ خلايا الشعار غير مدموجة")
            return False
        
        # فحص محتوى مساحة الشعار
        logo_cell = ws['A1']
        logo_content = logo_cell.value
        logo_fill = logo_cell.fill
        
        print(f"   محتوى مساحة الشعار: {logo_content}")
        print(f"   لون خلفية الشعار: {logo_fill.start_color}")
        
        # فحص ارتفاع الصفوف
        heights_ok = 0
        for row in range(1, 5):
            height = ws.row_dimensions[row].height
            print(f"   ارتفاع الصف {row}: {height}")
            if height and height >= 20:
                heights_ok += 1
        
        print(f"   ارتفاعات الصفوف مناسبة: {heights_ok}/4")
        
        return logo_merged and heights_ok >= 3
        
    except Exception as e:
        print(f"❌ خطأ في فحص مساحة الشعار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للتحقق"""
    print("🔍 التحقق الشامل من تنسيق صفحة الحساب")
    print("=" * 60)
    
    # حذف ملف الاختبار إن وجد
    test_file = "accounting_system.xlsx"
    if os.path.exists(test_file):
        try:
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار السابق")
        except:
            pass
    
    tests = [
        ("هيكل صفحة الحساب", verify_account_structure),
        ("التنسيق والألوان", verify_formatting),
        ("إضافة المستندات", verify_document_addition),
        ("الصيغ والحسابات", verify_formulas),
        ("مساحة الشعار", verify_logo_space)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_function():
                print(f"✅ اختبار {test_name}: نجح")
                passed_tests += 1
            else:
                print(f"❌ اختبار {test_name}: فشل")
        except Exception as e:
            print(f"❌ اختبار {test_name}: خطأ - {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج التحقق النهائية: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 تنسيق صفحة الحساب مثالي!")
        print("\n✅ جميع العناصر تعمل بشكل صحيح:")
        print("   - مساحة الشعار (4 صفوف)")
        print("   - ترويسة منسقة ومرتبة")
        print("   - عناوين أعمدة غامقة وملونة")
        print("   - رصيد افتتاحي بتنسيق مميز")
        print("   - إضافة مستندات تعمل")
        print("   - صيغ المجموع صحيحة")
        print("   - ترحيل الرصيد بين الأقسام")
        print("   - تنسيق ثلاث خانات عشرية")
        
        return True
    else:
        print("⚠️ تنسيق صفحة الحساب يحتاج تحسين")
        print(f"نجح {passed_tests} من أصل {total_tests} اختبار")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للإغلاق...")
    sys.exit(0 if success else 1)
