@echo off
chcp 65001 >nul
title Verify Distribution - Accounting System

echo ========================================
echo    Verify Distribution Readiness
echo    Accounting System
echo ========================================
echo.

echo Checking distribution readiness...
echo.

REM Use the specific Python path
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Check if Python exists
if exist %PYTHON_PATH% (
    echo Python found, running verification...
    echo.
    
    REM Run the verification script
    %PYTHON_PATH% verify_distribution.py
    
) else (
    echo ERROR: Python not found at expected location
    echo Expected: %PYTHON_PATH%
    pause
    exit /b 1
)

echo.
echo Verification completed.
pause
