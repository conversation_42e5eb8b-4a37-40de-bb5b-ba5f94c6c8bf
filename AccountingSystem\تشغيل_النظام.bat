@echo off
chcp 65001 >nul
title نظام إدارة المستندات المحاسبية - وزارة الصحة الأردنية

cls
echo.
echo ========================================
echo    نظام إدارة المستندات المحاسبية
echo        وزارة الصحة الأردنية
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت Python 3.7 أو أحدث من:
    echo https://www.python.org/downloads/
    echo.
    echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM التحقق من وجود المكتبات المطلوبة
echo 🔍 فحص المكتبات المطلوبة...
python -c "import openpyxl" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ مكتبة openpyxl غير مثبتة
    echo 📦 جاري تثبيت المكتبات المطلوبة...
    python -m pip install openpyxl ttkthemes
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
)

echo ✅ جميع المكتبات متوفرة
echo.

REM التحقق من وجود الملفات المطلوبة
if not exist "main.py" (
    if not exist "start.py" (
        if not exist "app.py" (
            echo ❌ خطأ: ملفات النظام غير موجودة
            pause
            exit /b 1
        ) else (
            set MAIN_FILE=app.py
        )
    ) else (
        set MAIN_FILE=start.py
    )
) else (
    set MAIN_FILE=main.py
)

echo 🚀 جاري تشغيل النظام...
echo.

REM تشغيل النظام
python %MAIN_FILE%

if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل النظام
    echo.
    echo للمساعدة:
    echo 1. تأكد من تثبيت Python 3.7 أو أحدث
    echo 2. تأكد من تثبيت المكتبات: pip install openpyxl ttkthemes
    echo 3. تأكد من وجود جميع ملفات النظام
    echo.
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
)

echo.
pause
