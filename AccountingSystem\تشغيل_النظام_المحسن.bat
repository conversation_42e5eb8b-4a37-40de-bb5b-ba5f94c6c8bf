@echo off
chcp 65001 >nul
title نظام إدارة المستندات المحاسبية - وزارة الصحة

echo ========================================
echo    نظام إدارة المستندات المحاسبية
echo    وزارة الصحة - المملكة الأردنية الهاشمية
echo ========================================
echo.

echo جاري بدء تشغيل النظام...
echo.

REM Use the specific Python path found
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Try different Python commands
set PYTHON_CMD=""

REM Check specific path first
if exist %PYTHON_PATH% (
    echo Python found at: %PYTHON_PATH%
    set "PYTHON_CMD=%PYTHON_PATH%"
    goto :python_found
)

REM Try standard python command
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found in PATH
    set "PYTHON_CMD=python"
    goto :python_found
)

REM Try py command
py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found via py command
    set "PYTHON_CMD=py"
    goto :python_found
)

REM Python not found
echo ERROR: Python not found
echo.
echo Please install Python 3.7 or newer from:
echo https://www.python.org/downloads/
echo.
echo Make sure to check "Add Python to PATH" during installation
echo.
pause
exit /b 1

:python_found
echo.
echo Checking required libraries...

REM Check openpyxl
%PYTHON_CMD% -c "import openpyxl; print('openpyxl available')" 2>nul
if %errorlevel% neq 0 (
    echo openpyxl not installed, installing...
    %PYTHON_CMD% -m pip install openpyxl
    if %errorlevel% neq 0 (
        echo Failed to install openpyxl
        echo Please run manually: %PYTHON_CMD% -m pip install openpyxl
        pause
        exit /b 1
    )
    echo openpyxl installed successfully
)

REM Check tkinter
%PYTHON_CMD% -c "import tkinter; print('tkinter available')" 2>nul
if %errorlevel% neq 0 (
    echo tkinter not available - required for GUI
    echo tkinter comes with Python usually, you may need to reinstall Python
    pause
    exit /b 1
)

echo.
echo All libraries available
echo.
echo Starting Accounting System...
echo.

REM Try to run the launcher
if exist "launcher.py" (
    echo Running launcher.py...
    %PYTHON_CMD% launcher.py
) else if exist "app.py" (
    echo Running app.py...
    %PYTHON_CMD% app.py
) else (
    echo ERROR: Launcher file not found!
    echo Looking for: launcher.py or app.py
    echo.
    echo Available files:
    dir *.py /b
    pause
    exit /b 1
)

echo.
echo System closed
echo.
pause
