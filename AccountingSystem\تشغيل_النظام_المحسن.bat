@echo off
chcp 65001 >nul
title نظام إدارة المستندات المحاسبية - وزارة الصحة

echo ========================================
echo    نظام إدارة المستندات المحاسبية
echo    وزارة الصحة - المملكة الأردنية الهاشمية
echo ========================================
echo.

echo جاري بدء تشغيل النظام...
echo.

REM Use the specific Python path found
set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

REM Try different Python commands
set PYTHON_CMD=""

REM Check specific path first
if exist %PYTHON_PATH% (
    echo تم العثور على Python في: %PYTHON_PATH%
    set PYTHON_CMD=%PYTHON_PATH%
    goto :python_found
)

REM Try standard python command
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo تم العثور على Python في PATH
    set PYTHON_CMD=python
    goto :python_found
)

REM Try py command
py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo تم العثور على Python عبر py command
    set PYTHON_CMD=py
    goto :python_found
)

REM Python not found
echo ❌ خطأ: Python غير موجود
echo.
echo يرجى تثبيت Python 3.7 أو أحدث من:
echo https://www.python.org/downloads/
echo.
echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
echo.
pause
exit /b 1

:python_found
echo.
echo فحص المكتبات المطلوبة...

REM Check openpyxl
%PYTHON_CMD% -c "import openpyxl; print('✅ openpyxl متوفر')" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ openpyxl غير مثبت، جاري التثبيت...
    %PYTHON_CMD% -m pip install openpyxl
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت openpyxl
        echo يرجى تشغيل الأمر التالي يدوياً:
        echo %PYTHON_CMD% -m pip install openpyxl
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت openpyxl بنجاح
)

REM Check tkinter
%PYTHON_CMD% -c "import tkinter; print('✅ tkinter متوفر')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ tkinter غير متوفر - مطلوب للواجهة الرسومية
    echo tkinter يأتي مع Python عادة، قد تحتاج لإعادة تثبيت Python
    pause
    exit /b 1
)

echo.
echo ✅ جميع المكتبات متوفرة
echo.
echo 🚀 بدء تشغيل نظام إدارة المستندات المحاسبية...
echo.

REM Try to run the launcher
if exist "launcher.py" (
    echo تشغيل launcher.py...
    %PYTHON_CMD% launcher.py
) else if exist "app.py" (
    echo تشغيل app.py...
    %PYTHON_CMD% app.py
) else (
    echo ❌ خطأ: ملف التشغيل غير موجود!
    echo البحث عن: launcher.py أو app.py
    echo.
    echo الملفات الموجودة:
    dir *.py /b
    pause
    exit /b 1
)

echo.
echo 👋 تم إغلاق النظام
echo.
pause
